#!/usr/bin/env python3
"""
Comprehensive Usage Examples for Advanced Seal Forgery Detection System

This file demonstrates various ways to use the seal forgery detection system,
from basic usage to advanced customization and evaluation.
"""

import os
import cv2
import numpy as np
from seal_forgery_detector import SealDatabase, ForgeryDetector, SealPreprocessor, AdvancedFeatureExtractor
from seal_evaluation import SealEvaluator

def basic_usage_example():
    """Basic usage example - detect forgery of a single image"""
    print("=== Basic Usage Example ===")
    
    # Initialize the system
    database = SealDatabase()
    detector = ForgeryDetector(database)
    
    # Check if we have any authentic seals in database
    seals = database.get_all_seals()
    if not seals:
        print("No authentic seals found in database.")
        print("Please add some authentic seals first using add_authentic_seal_example()")
        return
    
    # Test image path (replace with your test image)
    test_image_path = "test_seal.jpg"
    
    if not os.path.exists(test_image_path):
        print(f"Test image not found: {test_image_path}")
        print("Please provide a valid test image path")
        return
    
    try:
        # Perform forgery detection
        results = detector.detect_forgery(test_image_path)
        
        # Display results
        print(f"\\nAnalysis Results for: {test_image_path}")
        print("=" * 50)
        
        if not results:
            print("No matching templates found in database")
            return
        
        for i, match in enumerate(results, 1):
            print(f"\\nRank {i}:")
            print(f"  Template ID: {match.template_id}")
            print(f"  Similarity Score: {match.similarity_score:.3f}")
            print(f"  Status: {'AUTHENTIC' if match.is_authentic else 'POTENTIAL FORGERY'}")
            print(f"  Confidence: {match.confidence:.1%}")
            print(f"  Template Path: {match.template_path}")
        
        # Overall verdict
        best_match = results[0]
        if best_match.is_authentic:
            print(f"\\n🟢 VERDICT: Seal appears AUTHENTIC (confidence: {best_match.confidence:.1%})")
        else:
            print(f"\\n🔴 VERDICT: Seal appears to be a FORGERY (confidence: {1-best_match.confidence:.1%})")
            
    except Exception as e:
        print(f"Error during detection: {e}")

def add_authentic_seal_example():
    """Example of adding authentic seals to the database"""
    print("\\n=== Adding Authentic Seal Example ===")
    
    database = SealDatabase()
    detector = ForgeryDetector(database)
    
    # Example authentic seal paths (replace with your authentic seal images)
    authentic_seals = [
        {
            'path': 'authentic_seals/government_seal.jpg',
            'id': 'gov_001',
            'name': 'Government Official Seal',
            'category': 'government',
            'description': 'Official government department seal'
        },
        {
            'path': 'authentic_seals/company_seal.jpg',
            'id': 'corp_001',
            'name': 'ABC Corporation Seal',
            'category': 'corporate',
            'description': 'Corporate business seal'
        }
    ]
    
    for seal_info in authentic_seals:
        if os.path.exists(seal_info['path']):
            try:
                # Load and process the image
                image = cv2.imread(seal_info['path'])
                if image is None:
                    print(f"Failed to load image: {seal_info['path']}")
                    continue
                
                # Preprocess the seal
                processed = detector.preprocessor.preprocess_seal(image)
                
                # Extract comprehensive features
                features = detector.feature_extractor.extract_comprehensive_features(processed)
                
                # Add to database
                database.add_seal(
                    seal_id=seal_info['id'],
                    name=seal_info['name'],
                    category=seal_info['category'],
                    image_path=seal_info['path'],
                    features=features,
                    description=seal_info['description']
                )
                
                print(f"✓ Added {seal_info['name']} to database")
                
            except Exception as e:
                print(f"✗ Failed to add {seal_info['name']}: {e}")
        else:
            print(f"✗ Image not found: {seal_info['path']}")

def advanced_preprocessing_example():
    """Example of advanced preprocessing techniques"""
    print("\\n=== Advanced Preprocessing Example ===")
    
    preprocessor = SealPreprocessor()
    
    # Load test image
    image_path = "test_seal.jpg"
    if not os.path.exists(image_path):
        print(f"Test image not found: {image_path}")
        return
    
    image = cv2.imread(image_path)
    if image is None:
        print("Failed to load image")
        return
    
    # Perform comprehensive preprocessing
    processed = preprocessor.preprocess_seal(image)
    
    # Display preprocessing results
    print("Preprocessing Results:")
    print(f"  Original image shape: {image.shape}")
    print(f"  Red mask extracted: {'Yes' if 'red_mask' in processed else 'No'}")
    print(f"  Circle detected: {'Yes' if processed.get('circle') is not None else 'No'}")
    
    if processed.get('circle'):
        x, y, r = processed['circle']
        print(f"  Circle center: ({x}, {y}), radius: {r}")
    
    print(f"  Polar transformation: {'Available' if 'polar' in processed else 'Not available'}")
    
    # Save preprocessing results (optional)
    save_preprocessing_results = False
    if save_preprocessing_results:
        cv2.imwrite('preprocessed_red_mask.jpg', processed['red_mask'])
        cv2.imwrite('preprocessed_binary.jpg', processed['binary'])
        if 'polar' in processed:
            cv2.imwrite('preprocessed_polar.jpg', processed['polar'])
        print("Preprocessing results saved to files")

def feature_extraction_example():
    """Example of detailed feature extraction"""
    print("\\n=== Feature Extraction Example ===")
    
    extractor = AdvancedFeatureExtractor()
    preprocessor = SealPreprocessor()
    
    # Load and preprocess image
    image_path = "test_seal.jpg"
    if not os.path.exists(image_path):
        print(f"Test image not found: {image_path}")
        return
    
    image = cv2.imread(image_path)
    processed = preprocessor.preprocess_seal(image)
    
    # Extract comprehensive features
    features = extractor.extract_comprehensive_features(processed)
    
    # Display feature extraction results
    print("Feature Extraction Results:")
    
    # SIFT features
    sift_kp = features.get('sift_keypoints', [])
    sift_desc = features.get('sift_descriptors')
    print(f"  SIFT keypoints: {len(sift_kp) if sift_kp else 0}")
    print(f"  SIFT descriptors shape: {sift_desc.shape if sift_desc is not None else 'None'}")
    
    # ORB features
    orb_kp = features.get('orb_keypoints', [])
    orb_desc = features.get('orb_descriptors')
    print(f"  ORB keypoints: {len(orb_kp) if orb_kp else 0}")
    print(f"  ORB descriptors shape: {orb_desc.shape if orb_desc is not None else 'None'}")
    
    # Texture features
    texture = features.get('texture')
    print(f"  Texture features shape: {texture.shape if texture is not None else 'None'}")
    
    # Edge features
    edge = features.get('edge')
    print(f"  Edge features shape: {edge.shape if edge is not None else 'None'}")
    
    # HOG features
    hog = features.get('hog')
    print(f"  HOG features shape: {hog.shape if hog is not None else 'None'}")
    
    # Text features
    text_info = features.get('text_info', [])
    text_strings = features.get('text_strings', [])
    print(f"  Text regions detected: {len(text_info)}")
    print(f"  Text strings: {text_strings}")

def batch_evaluation_example():
    """Example of batch evaluation and performance testing"""
    print("\\n=== Batch Evaluation Example ===")
    
    # Initialize system
    database = SealDatabase()
    detector = ForgeryDetector(database)
    evaluator = SealEvaluator(detector)
    
    # Test data directory structure
    test_data_path = "test_data"
    
    if not os.path.exists(test_data_path):
        print(f"Test data directory not found: {test_data_path}")
        print("Please create the following structure:")
        print("test_data/")
        print("├── authentic/")
        print("│   ├── seal1.jpg")
        print("│   └── seal2.jpg")
        print("└── forged/")
        print("    ├── fake1.jpg")
        print("    └── fake2.jpg")
        return
    
    try:
        # Run batch evaluation
        print("Running batch evaluation...")
        results = evaluator.batch_evaluate(test_data_path)
        
        # Calculate metrics
        metrics = evaluator.calculate_metrics(results)
        
        # Display results
        print("\\nEvaluation Results:")
        print(f"  Total authentic tested: {len(results['authentic'])}")
        print(f"  Total forged tested: {len(results['forged'])}")
        
        if metrics:
            print("\\nPerformance Metrics:")
            print(f"  Accuracy: {metrics['accuracy']:.3f}")
            print(f"  Precision: {metrics['precision']:.3f}")
            print(f"  Recall: {metrics['recall']:.3f}")
            print(f"  F1-Score: {metrics['f1_score']:.3f}")
        
        # Generate detailed report
        report = evaluator.generate_report(results, "evaluation_report.json")
        print("\\nDetailed report saved to: evaluation_report.json")
        
        # Generate plots
        try:
            evaluator.plot_results(results, "evaluation_plots.png")
            print("Evaluation plots saved to: evaluation_plots.png")
        except Exception as e:
            print(f"Failed to generate plots: {e}")
            
    except Exception as e:
        print(f"Evaluation failed: {e}")

def database_management_example():
    """Example of database management operations"""
    print("\\n=== Database Management Example ===")
    
    database = SealDatabase()
    
    # Get all seals
    seals = database.get_all_seals()
    print(f"Total seals in database: {len(seals)}")
    
    # Display seal information
    for seal in seals[:5]:  # Show first 5 seals
        print(f"\\nSeal: {seal['name']} ({seal['id']})")
        print(f"  Category: {seal['category']}")
        print(f"  Created: {seal['created_at']}")
        print(f"  Image size: {seal.get('image_width', 'N/A')}x{seal.get('image_height', 'N/A')}")
    
    # Get categories
    categories = database.get_categories()
    print(f"\\nAvailable categories: {[cat['name'] for cat in categories]}")
    
    # Get seals by category
    if categories:
        category_name = categories[0]['name']
        category_seals = database.get_seals_by_category(category_name)
        print(f"\\nSeals in '{category_name}' category: {len(category_seals)}")
    
    # Get detection history
    history = database.get_detection_history(limit=10)
    print(f"\\nRecent detections: {len(history)}")
    
    for record in history[:3]:  # Show first 3 records
        print(f"  {record['detection_time']}: {record['input_image_path']} -> "
              f"{'AUTHENTIC' if record['is_authentic'] else 'FORGERY'} "
              f"(confidence: {record['confidence']:.1%})")

def custom_similarity_example():
    """Example of custom similarity calculation"""
    print("\\n=== Custom Similarity Example ===")
    
    # This example shows how to customize the similarity calculation
    # by accessing the detector's internal methods
    
    database = SealDatabase()
    detector = ForgeryDetector(database)
    
    # Load two images for comparison
    image1_path = "seal1.jpg"
    image2_path = "seal2.jpg"
    
    if not (os.path.exists(image1_path) and os.path.exists(image2_path)):
        print("Please provide two seal images for comparison")
        return
    
    try:
        # Process both images
        image1 = cv2.imread(image1_path)
        image2 = cv2.imread(image2_path)
        
        processed1 = detector.preprocessor.preprocess_seal(image1)
        processed2 = detector.preprocessor.preprocess_seal(image2)
        
        features1 = detector.feature_extractor.extract_comprehensive_features(processed1)
        features2 = detector.feature_extractor.extract_comprehensive_features(processed2)
        
        # Calculate detailed similarity breakdown
        similarity_breakdown = detector.comprehensive_similarity(features1, features2)
        
        print("Detailed Similarity Analysis:")
        for feature_type, score in similarity_breakdown.items():
            print(f"  {feature_type}: {score:.3f}")
        
        overall_similarity = similarity_breakdown.get('overall', 0.0)
        print(f"\\nOverall Similarity: {overall_similarity:.3f}")
        
        # Determine authenticity
        is_authentic = overall_similarity >= detector.authenticity_threshold
        print(f"Authenticity: {'AUTHENTIC' if is_authentic else 'POTENTIAL FORGERY'}")
        
    except Exception as e:
        print(f"Similarity calculation failed: {e}")

def main():
    """Run all examples"""
    print("Advanced Seal Forgery Detection System - Usage Examples")
    print("=" * 60)
    
    # Run examples
    try:
        add_authentic_seal_example()
        basic_usage_example()
        advanced_preprocessing_example()
        feature_extraction_example()
        database_management_example()
        custom_similarity_example()
        batch_evaluation_example()
        
    except KeyboardInterrupt:
        print("\\nExamples interrupted by user")
    except Exception as e:
        print(f"\\nError running examples: {e}")
    
    print("\\n" + "=" * 60)
    print("Examples completed. Check the output above for results.")

if __name__ == "__main__":
    main()
