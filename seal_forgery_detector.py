import cv2
import numpy as np
import os
import sqlite3
from typing import List, Tu<PERSON>, Dict, Optional
from dataclasses import dataclass
from sklearn.metrics.pairwise import cosine_similarity
import pickle
from difflib import SequenceMatcher
import re

# OCR imports (with fallback if not available)
try:
    from paddleocr import PaddleOCR
    PADDLEOCR_AVAILABLE = True
except ImportError:
    PADDLEOCR_AVAILABLE = False
    print("PaddleOCR not available. Text recognition features will be disabled.")

try:
    import pytesseract
    TESSERACT_AVAILABLE = True
except ImportError:
    TESSERACT_AVAILABLE = False
    print("Tesseract not available. Fallback text recognition disabled.")

@dataclass
class SealMatch:
    """Result of seal matching"""
    template_id: str
    similarity_score: float
    template_path: str
    is_authentic: bool
    confidence: float

class SealPreprocessor:
    """Advanced seal image preprocessing with enhanced algorithms"""

    def __init__(self):
        # Enhanced red color ranges for better seal detection
        self.red_lower = np.array([0, 50, 50])
        self.red_upper = np.array([10, 255, 255])
        self.red_lower2 = np.array([170, 50, 50])
        self.red_upper2 = np.array([180, 255, 255])

        # Additional red ranges for different lighting conditions
        self.red_lower3 = np.array([0, 30, 30])
        self.red_upper3 = np.array([15, 255, 255])
        self.red_lower4 = np.array([165, 30, 30])
        self.red_upper4 = np.array([180, 255, 255])
    
    def extract_red_regions(self, image: np.ndarray) -> np.ndarray:
        """Enhanced red seal region extraction with multiple color spaces"""
        # HSV color space extraction
        hsv = cv2.cvtColor(image, cv2.COLOR_BGR2HSV)

        # Create masks for multiple red color ranges
        mask1 = cv2.inRange(hsv, self.red_lower, self.red_upper)
        mask2 = cv2.inRange(hsv, self.red_lower2, self.red_upper2)
        mask3 = cv2.inRange(hsv, self.red_lower3, self.red_upper3)
        mask4 = cv2.inRange(hsv, self.red_lower4, self.red_upper4)

        # Combine all masks
        red_mask_hsv = cv2.bitwise_or(cv2.bitwise_or(mask1, mask2), cv2.bitwise_or(mask3, mask4))

        # Additional extraction using LAB color space for better red detection
        lab = cv2.cvtColor(image, cv2.COLOR_BGR2LAB)
        # In LAB, red colors have high A values
        red_mask_lab = cv2.inRange(lab, np.array([0, 130, 0]), np.array([255, 255, 255]))

        # Combine HSV and LAB masks
        red_mask = cv2.bitwise_or(red_mask_hsv, red_mask_lab)

        # Ensure mask is uint8
        red_mask = red_mask.astype(np.uint8)

        # Enhanced morphological operations
        kernel_small = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (3, 3))
        kernel_medium = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (5, 5))

        # Remove noise
        red_mask = cv2.morphologyEx(red_mask, cv2.MORPH_OPEN, kernel_small)
        # Fill gaps
        red_mask = cv2.morphologyEx(red_mask, cv2.MORPH_CLOSE, kernel_medium)
        # Final cleanup
        red_mask = cv2.medianBlur(red_mask, 3)

        # Ensure final result is uint8
        red_mask = red_mask.astype(np.uint8)

        return red_mask
    
    def detect_circular_seal(self, mask: np.ndarray) -> Optional[Tuple[int, int, int]]:
        """Enhanced circular seal detection with multiple approaches"""
        # Apply Gaussian blur to reduce noise for better circle detection
        blurred_mask = cv2.GaussianBlur(mask, (9, 9), 2)

        # Primary Hough circle detection
        circles = cv2.HoughCircles(
            blurred_mask, cv2.HOUGH_GRADIENT, dp=1, minDist=100,
            param1=50, param2=30, minRadius=50, maxRadius=300
        )

        # If no circles found, try with relaxed parameters
        if circles is None:
            circles = cv2.HoughCircles(
                blurred_mask, cv2.HOUGH_GRADIENT, dp=1, minDist=80,
                param1=40, param2=25, minRadius=30, maxRadius=400
            )

        # If still no circles, try gradient-based approach
        if circles is None:
            circles = cv2.HoughCircles(
                blurred_mask, cv2.HOUGH_GRADIENT_ALT, dp=1, minDist=100,
                param1=300, param2=0.85, minRadius=50, maxRadius=300
            )

        if circles is not None:
            circles = np.round(circles[0, :]).astype("int")

            # Filter circles based on quality metrics
            valid_circles = []
            for circle in circles:
                x, y, r = circle
                # Check if circle is within image bounds
                if (x - r >= 0 and y - r >= 0 and
                    x + r < mask.shape[1] and y + r < mask.shape[0]):

                    # Calculate circle quality (how much of the circle perimeter is filled)
                    circle_mask = np.zeros_like(mask)
                    cv2.circle(circle_mask, (x, y), r, 255, 2)
                    intersection = cv2.bitwise_and(mask, circle_mask)
                    quality = np.sum(intersection) / (2 * np.pi * r * 255)

                    if quality > 0.3:  # At least 30% of perimeter should be filled
                        valid_circles.append((circle, quality))

            if valid_circles:
                # Return the circle with best quality, or largest if qualities are similar
                valid_circles.sort(key=lambda x: (x[1], x[0][2]), reverse=True)
                return tuple(valid_circles[0][0])

        return None
    
    def polar_transform(self, image: np.ndarray, center: Tuple[int, int], radius: int) -> np.ndarray:
        """Convert circular seal to polar coordinates for rotation invariance with data type safety"""
        x, y = center

        # Ensure input image is uint8
        if image.dtype != np.uint8:
            image = cv2.convertScaleAbs(image)

        # Validate inputs and prevent overflow
        if radius <= 0:
            radius = 50  # Default radius
        if radius > min(image.shape[:2]) // 2:
            radius = min(image.shape[:2]) // 2

        # Limit radius to prevent memory issues and overflow
        radius = min(radius, 200)

        # Create polar coordinate mapping
        theta = np.linspace(0, 2*np.pi, 360)
        r = np.linspace(0, radius, radius)

        polar_image = np.zeros((radius, 360), dtype=np.uint8)

        try:
            for i, radius_val in enumerate(r):
                for j, angle in enumerate(theta):
                    px = int(x + radius_val * np.cos(angle))
                    py = int(y + radius_val * np.sin(angle))

                    # Ensure coordinates are within image bounds
                    if 0 <= px < image.shape[1] and 0 <= py < image.shape[0]:
                        # Get pixel value and ensure it's within uint8 range
                        pixel_val = image[py, px]

                        # Handle different data types safely
                        if isinstance(pixel_val, (np.ndarray, list, tuple)):
                            pixel_val = pixel_val[0] if len(pixel_val) > 0 else 0

                        # Ensure value is within uint8 range (0-255)
                        pixel_val = int(pixel_val)
                        pixel_val = max(0, min(pixel_val, 255))

                        polar_image[i, j] = pixel_val

        except Exception as e:
            print(f"Error in polar transform: {e}")
            # Return a valid empty polar image on error
            return np.zeros((radius, 360), dtype=np.uint8)

        return polar_image.astype(np.uint8)
    
    def preprocess_seal(self, image: np.ndarray) -> Dict:
        """Complete preprocessing pipeline with data type safety"""
        # Ensure input image is in correct format
        if image is None:
            raise ValueError("Input image is None")

        # Ensure image is uint8
        if image.dtype != np.uint8:
            image = cv2.convertScaleAbs(image)

        # Extract red regions
        red_mask = self.extract_red_regions(image)

        # Detect circular seal
        circle = self.detect_circular_seal(red_mask)

        # Convert to grayscale
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        gray = gray.astype(np.uint8)

        # Apply red mask to grayscale
        masked_gray = cv2.bitwise_and(gray, gray, mask=red_mask)
        masked_gray = masked_gray.astype(np.uint8)

        # Binarization with error handling
        try:
            _, binary = cv2.threshold(masked_gray, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
        except cv2.error:
            # Fallback to simple thresholding if OTSU fails
            _, binary = cv2.threshold(masked_gray, 127, 255, cv2.THRESH_BINARY)

        binary = binary.astype(np.uint8)

        result = {
            'original': image.astype(np.uint8),
            'red_mask': red_mask.astype(np.uint8),
            'gray': gray.astype(np.uint8),
            'masked_gray': masked_gray.astype(np.uint8),
            'binary': binary.astype(np.uint8),
            'circle': circle
        }

        # Add polar transform if circle detected
        if circle:
            center = (circle[0], circle[1])
            radius = circle[2]
            try:
                polar_result = self.polar_transform(binary, center, radius)
                result['polar'] = polar_result.astype(np.uint8)
            except Exception as e:
                print(f"Polar transform failed: {e}")
                # Continue without polar transform

        return result

class TextRecognizer:
    """OCR-based text recognition for seals"""

    def __init__(self):
        self.paddle_ocr = None
        if PADDLEOCR_AVAILABLE:
            try:
                # Initialize PaddleOCR with correct parameters for current version
                self.paddle_ocr = PaddleOCR(use_angle_cls=True, lang='ch')
            except Exception as e:
                print(f"Failed to initialize PaddleOCR: {e}")
                self.paddle_ocr = None
                # Try with minimal parameters as fallback
                try:
                    self.paddle_ocr = PaddleOCR(lang='ch')
                    print("PaddleOCR initialized with minimal parameters")
                except Exception as e2:
                    print(f"PaddleOCR fallback initialization also failed: {e2}")
                    self.paddle_ocr = None

    def extract_text_paddleocr(self, image: np.ndarray) -> List[Dict]:
        """Extract text using PaddleOCR"""
        if not self.paddle_ocr:
            return []

        try:
            # Try newer predict method first, fallback to ocr method
            try:
                results = self.paddle_ocr.predict(image)
            except AttributeError:
                # Fallback to older ocr method
                results = self.paddle_ocr.ocr(image, cls=True)

            text_info = []

            if results and results[0]:
                for line in results[0]:
                    if len(line) >= 2:
                        bbox = line[0]  # Bounding box coordinates
                        text_data = line[1]  # (text, confidence)

                        if len(text_data) >= 2:
                            text = text_data[0]
                            confidence = text_data[1]

                            text_info.append({
                                'text': text,
                                'confidence': confidence,
                                'bbox': bbox
                            })

            return text_info

        except Exception as e:
            print(f"PaddleOCR extraction failed: {e}")
            return []

    def extract_text_tesseract(self, image: np.ndarray) -> List[Dict]:
        """Extract text using Tesseract OCR as fallback"""
        if not TESSERACT_AVAILABLE:
            return []

        try:
            # Configure Tesseract for better Chinese character recognition
            config = '--oem 3 --psm 6 -c tessedit_char_whitelist=0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz中文'

            # Extract text with bounding boxes
            data = pytesseract.image_to_data(image, config=config, output_type=pytesseract.Output.DICT)

            text_info = []
            for i in range(len(data['text'])):
                if int(data['conf'][i]) > 30:  # Confidence threshold
                    text = data['text'][i].strip()
                    if text:
                        bbox = [
                            [data['left'][i], data['top'][i]],
                            [data['left'][i] + data['width'][i], data['top'][i]],
                            [data['left'][i] + data['width'][i], data['top'][i] + data['height'][i]],
                            [data['left'][i], data['top'][i] + data['height'][i]]
                        ]

                        text_info.append({
                            'text': text,
                            'confidence': data['conf'][i] / 100.0,
                            'bbox': bbox
                        })

            return text_info

        except Exception as e:
            print(f"Tesseract extraction failed: {e}")
            return []

    def extract_text(self, image: np.ndarray) -> List[Dict]:
        """Extract text using available OCR engines"""
        # Try PaddleOCR first (better for Chinese text)
        text_info = self.extract_text_paddleocr(image)

        # Fallback to Tesseract if PaddleOCR fails or unavailable
        if not text_info:
            text_info = self.extract_text_tesseract(image)

        return text_info

    def calculate_text_similarity(self, text1: str, text2: str) -> float:
        """Calculate similarity between two text strings"""
        if not text1 or not text2:
            return 0.0

        # Normalize text (remove spaces, convert to lowercase)
        text1_norm = re.sub(r'\s+', '', text1.lower())
        text2_norm = re.sub(r'\s+', '', text2.lower())

        # Use sequence matcher for similarity
        similarity = SequenceMatcher(None, text1_norm, text2_norm).ratio()

        return similarity

    def compare_text_lists(self, texts1: List[str], texts2: List[str]) -> float:
        """Compare two lists of extracted texts"""
        if not texts1 or not texts2:
            return 0.0

        # Find best matches between text lists
        max_similarities = []

        for text1 in texts1:
            best_similarity = 0.0
            for text2 in texts2:
                similarity = self.calculate_text_similarity(text1, text2)
                best_similarity = max(best_similarity, similarity)
            max_similarities.append(best_similarity)

        # Return average of best similarities
        return np.mean(max_similarities) if max_similarities else 0.0

class AdvancedFeatureExtractor:
    """Advanced feature extraction with deep learning and traditional CV techniques"""

    def __init__(self):
        self.sift = cv2.SIFT_create(nfeatures=2000, contrastThreshold=0.04)
        self.orb = cv2.ORB_create(nfeatures=1500, scaleFactor=1.2, nlevels=8)
        self.akaze = cv2.AKAZE_create()
        self.brisk = cv2.BRISK_create()

        # Initialize HOG descriptor for shape analysis
        self.hog = cv2.HOGDescriptor(
            _winSize=(64, 64),
            _blockSize=(16, 16),
            _blockStride=(8, 8),
            _cellSize=(8, 8),
            _nbins=9
        )

        # Initialize text recognizer
        self.text_recognizer = TextRecognizer()
    
    def extract_sift_features(self, image: np.ndarray) -> Tuple[np.ndarray, np.ndarray]:
        """Extract SIFT keypoints and descriptors"""
        keypoints, descriptors = self.sift.detectAndCompute(image, None)
        return keypoints, descriptors
    
    def extract_orb_features(self, image: np.ndarray) -> Tuple[np.ndarray, np.ndarray]:
        """Extract ORB keypoints and descriptors"""
        keypoints, descriptors = self.orb.detectAndCompute(image, None)
        return keypoints, descriptors

    def extract_akaze_features(self, image: np.ndarray) -> Tuple[np.ndarray, np.ndarray]:
        """Extract AKAZE keypoints and descriptors"""
        keypoints, descriptors = self.akaze.detectAndCompute(image, None)
        return keypoints, descriptors

    def extract_brisk_features(self, image: np.ndarray) -> Tuple[np.ndarray, np.ndarray]:
        """Extract BRISK keypoints and descriptors"""
        keypoints, descriptors = self.brisk.detectAndCompute(image, None)
        return keypoints, descriptors

    def extract_hog_features(self, image: np.ndarray) -> np.ndarray:
        """Extract HOG (Histogram of Oriented Gradients) features with data type safety"""
        try:
            # Ensure input image is in correct format
            if len(image.shape) == 3:
                image = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)

            # Ensure image is uint8 to prevent overflow
            if image.dtype != np.uint8:
                image = cv2.convertScaleAbs(image)

            # Resize image to fixed size for HOG
            resized = cv2.resize(image, (64, 64))

            # Ensure resized image is still uint8
            resized = resized.astype(np.uint8)

            # Compute HOG features
            hog_features = self.hog.compute(resized)

            if hog_features is not None:
                # Ensure HOG features are in valid range
                hog_features = hog_features.flatten()
                # Clip values to prevent overflow in downstream processing
                hog_features = np.clip(hog_features, -1e6, 1e6)
                return hog_features.astype(np.float32)
            else:
                return np.array([], dtype=np.float32)

        except Exception as e:
            print(f"HOG feature extraction failed: {e}")
            return np.array([], dtype=np.float32)
    
    def extract_texture_features(self, image: np.ndarray) -> np.ndarray:
        """Extract texture features using Local Binary Patterns with overflow protection"""
        def lbp(image, radius=3, n_points=24):
            # Ensure n_points doesn't exceed what uint8 can handle
            n_points = min(n_points, 8)  # Limit to 8 points to prevent overflow

            h, w = image.shape
            lbp_image = np.zeros((h, w), dtype=np.uint8)

            for i in range(radius, h - radius):
                for j in range(radius, w - radius):
                    center = image[i, j]
                    code = 0

                    for k in range(n_points):
                        angle = 2 * np.pi * k / n_points
                        x = int(i + radius * np.cos(angle))
                        y = int(j + radius * np.sin(angle))

                        # Ensure coordinates are within bounds
                        x = max(0, min(x, h - 1))
                        y = max(0, min(y, w - 1))

                        if image[x, y] >= center:
                            code |= (1 << k)

                    # Ensure code fits in uint8 range
                    lbp_image[i, j] = min(code, 255)

            return lbp_image

        # Ensure input image is in correct format
        if len(image.shape) == 3:
            image = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)

        # Ensure image is uint8
        if image.dtype != np.uint8:
            image = cv2.convertScaleAbs(image)

        lbp_image = lbp(image)
        hist, _ = np.histogram(lbp_image.ravel(), bins=256, range=(0, 256))

        # Avoid division by zero
        hist_sum = np.sum(hist)
        if hist_sum > 0:
            return hist / hist_sum
        else:
            return np.zeros(256, dtype=np.float32)
    
    def extract_edge_features(self, image: np.ndarray) -> np.ndarray:
        """Extract edge-based features"""
        # Canny edge detection
        edges = cv2.Canny(image, 50, 150)
        
        # Edge density in different regions
        h, w = edges.shape
        regions = [
            edges[:h//2, :w//2],  # Top-left
            edges[:h//2, w//2:],  # Top-right
            edges[h//2:, :w//2],  # Bottom-left
            edges[h//2:, w//2:]   # Bottom-right
        ]
        
        edge_densities = [np.sum(region) / region.size for region in regions]
        
        # Edge orientation histogram
        sobelx = cv2.Sobel(image, cv2.CV_64F, 1, 0, ksize=3)
        sobely = cv2.Sobel(image, cv2.CV_64F, 0, 1, ksize=3)
        orientation = np.arctan2(sobely, sobelx)
        
        hist, _ = np.histogram(orientation.ravel(), bins=36, range=(-np.pi, np.pi))
        orientation_hist = hist / np.sum(hist)
        
        return np.concatenate([edge_densities, orientation_hist])
    
    def extract_comprehensive_features(self, processed_seal: Dict) -> Dict:
        """Extract all features from processed seal including text"""
        features = {}

        binary = processed_seal['binary']
        original = processed_seal['original']

        # SIFT features
        sift_kp, sift_desc = self.extract_sift_features(binary)
        features['sift_keypoints'] = sift_kp
        features['sift_descriptors'] = sift_desc

        # ORB features
        orb_kp, orb_desc = self.extract_orb_features(binary)
        features['orb_keypoints'] = orb_kp
        features['orb_descriptors'] = orb_desc

        # AKAZE features
        akaze_kp, akaze_desc = self.extract_akaze_features(binary)
        features['akaze_keypoints'] = akaze_kp
        features['akaze_descriptors'] = akaze_desc

        # BRISK features
        brisk_kp, brisk_desc = self.extract_brisk_features(binary)
        features['brisk_keypoints'] = brisk_kp
        features['brisk_descriptors'] = brisk_desc

        # HOG features
        features['hog'] = self.extract_hog_features(binary)

        # Texture features
        features['texture'] = self.extract_texture_features(binary)

        # Edge features
        features['edge'] = self.extract_edge_features(binary)

        # Polar features if available
        if 'polar' in processed_seal:
            features['polar_texture'] = self.extract_texture_features(processed_seal['polar'])

        # Text features
        text_info = self.text_recognizer.extract_text(original)
        features['text_info'] = text_info
        features['text_strings'] = [info['text'] for info in text_info]

        return features

class SealDatabase:
    """Database management for authentic seals"""
    
    def __init__(self, db_path: str = "seal_database.db"):
        self.db_path = db_path
        self.init_database()
    
    def init_database(self):
        """Initialize enhanced SQLite database with metadata support"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        # Main seals table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS seals (
                id TEXT PRIMARY KEY,
                name TEXT NOT NULL,
                category TEXT DEFAULT 'general',
                description TEXT,
                image_path TEXT NOT NULL,
                features_path TEXT NOT NULL,
                image_hash TEXT,
                file_size INTEGER,
                image_width INTEGER,
                image_height INTEGER,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')

        # Categories table for better organization
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS categories (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT UNIQUE NOT NULL,
                description TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')

        # Insert default categories
        default_categories = [
            ('government', 'Government and official seals'),
            ('corporate', 'Corporate and business seals'),
            ('personal', 'Personal and individual seals'),
            ('academic', 'Educational institution seals'),
            ('legal', 'Legal and notary seals'),
            ('medical', 'Medical and healthcare seals'),
            ('other', 'Other types of seals')
        ]

        cursor.executemany('''
            INSERT OR IGNORE INTO categories (name, description) VALUES (?, ?)
        ''', default_categories)

        # Detection history table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS detection_history (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                input_image_path TEXT NOT NULL,
                best_match_id TEXT,
                similarity_score REAL,
                is_authentic BOOLEAN,
                confidence REAL,
                detection_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (best_match_id) REFERENCES seals (id)
            )
        ''')

        # Create indexes for better performance
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_seals_category ON seals(category)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_seals_created ON seals(created_at)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_history_time ON detection_history(detection_time)')

        conn.commit()
        conn.close()
    
    def add_seal(self, seal_id: str, name: str, category: str,
                 image_path: str, features: Dict, description: str = ""):
        """Add a new authentic seal to database with enhanced metadata"""
        import hashlib

        features_path = f"features/{seal_id}.pkl"
        os.makedirs("features", exist_ok=True)

        # Save features
        with open(features_path, 'wb') as f:
            pickle.dump(features, f)

        # Calculate image metadata
        try:
            import cv2
            image = cv2.imread(image_path)
            if image is not None:
                height, width = image.shape[:2]
                file_size = os.path.getsize(image_path)

                # Calculate image hash for duplicate detection
                with open(image_path, 'rb') as f:
                    image_hash = hashlib.md5(f.read()).hexdigest()
            else:
                height = width = file_size = 0
                image_hash = ""
        except Exception:
            height = width = file_size = 0
            image_hash = ""

        # Add to database
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        cursor.execute('''
            INSERT OR REPLACE INTO seals
            (id, name, category, description, image_path, features_path,
             image_hash, file_size, image_width, image_height, updated_at)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        ''', (seal_id, name, category, description, image_path, features_path,
              image_hash, file_size, width, height))

        conn.commit()
        conn.close()

    def get_categories(self) -> List[Dict]:
        """Get all available categories"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        cursor.execute('SELECT name, description FROM categories ORDER BY name')
        rows = cursor.fetchall()

        categories = []
        for row in rows:
            categories.append({
                'name': row[0],
                'description': row[1]
            })

        conn.close()
        return categories

    def get_seals_by_category(self, category: str) -> List[Dict]:
        """Get seals filtered by category"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        cursor.execute('SELECT * FROM seals WHERE category = ? ORDER BY created_at DESC', (category,))
        rows = cursor.fetchall()

        seals = []
        for row in rows:
            seal = {
                'id': row[0],
                'name': row[1],
                'category': row[2],
                'description': row[3],
                'image_path': row[4],
                'features_path': row[5],
                'image_hash': row[6],
                'file_size': row[7],
                'image_width': row[8],
                'image_height': row[9],
                'created_at': row[10],
                'updated_at': row[11]
            }
            seals.append(seal)

        conn.close()
        return seals

    def record_detection(self, input_image_path: str, best_match: 'SealMatch'):
        """Record detection result in history"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        cursor.execute('''
            INSERT INTO detection_history
            (input_image_path, best_match_id, similarity_score, is_authentic, confidence)
            VALUES (?, ?, ?, ?, ?)
        ''', (input_image_path, best_match.template_id, best_match.similarity_score,
              best_match.is_authentic, best_match.confidence))

        conn.commit()
        conn.close()

    def get_detection_history(self, limit: int = 100) -> List[Dict]:
        """Get recent detection history"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        cursor.execute('''
            SELECT h.*, s.name as template_name
            FROM detection_history h
            LEFT JOIN seals s ON h.best_match_id = s.id
            ORDER BY h.detection_time DESC
            LIMIT ?
        ''', (limit,))

        rows = cursor.fetchall()

        history = []
        for row in rows:
            record = {
                'id': row[0],
                'input_image_path': row[1],
                'best_match_id': row[2],
                'similarity_score': row[3],
                'is_authentic': row[4],
                'confidence': row[5],
                'detection_time': row[6],
                'template_name': row[7]
            }
            history.append(record)

        conn.close()
        return history
    
    def get_all_seals(self) -> List[Dict]:
        """Get all seals from database with enhanced metadata"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        cursor.execute('SELECT * FROM seals ORDER BY created_at DESC')
        rows = cursor.fetchall()

        seals = []
        for row in rows:
            seal = {
                'id': row[0],
                'name': row[1],
                'category': row[2],
                'description': row[3],
                'image_path': row[4],
                'features_path': row[5],
                'image_hash': row[6],
                'file_size': row[7],
                'image_width': row[8],
                'image_height': row[9],
                'created_at': row[10],
                'updated_at': row[11]
            }
            seals.append(seal)

        conn.close()
        return seals
    
    def load_features(self, features_path: str) -> Dict:
        """Load features from file"""
        with open(features_path, 'rb') as f:
            return pickle.load(f)

class ForgeryDetector:
    """Advanced forgery detection system"""
    
    def __init__(self, database: SealDatabase):
        self.database = database
        self.preprocessor = SealPreprocessor()
        self.feature_extractor = AdvancedFeatureExtractor()
        
        # Thresholds for forgery detection
        self.authenticity_threshold = 0.7
        self.sift_match_threshold = 0.75
    
    def match_sift_features(self, desc1: np.ndarray, desc2: np.ndarray) -> float:
        """Match SIFT descriptors using FLANN matcher"""
        if desc1 is None or desc2 is None:
            return 0.0
        
        # FLANN matcher
        FLANN_INDEX_KDTREE = 1
        index_params = dict(algorithm=FLANN_INDEX_KDTREE, trees=5)
        search_params = dict(checks=50)
        flann = cv2.FlannBasedMatcher(index_params, search_params)
        
        try:
            matches = flann.knnMatch(desc1, desc2, k=2)
            
            # Apply Lowe's ratio test
            good_matches = []
            for match_pair in matches:
                if len(match_pair) == 2:
                    m, n = match_pair
                    if m.distance < self.sift_match_threshold * n.distance:
                        good_matches.append(m)
            
            # Calculate similarity score
            if len(desc1) > 0:
                return len(good_matches) / len(desc1)
            return 0.0
            
        except Exception:
            return 0.0
    
    def calculate_texture_similarity(self, texture1: np.ndarray, texture2: np.ndarray) -> float:
        """Calculate texture similarity using cosine similarity"""
        return cosine_similarity([texture1], [texture2])[0][0]
    
    def calculate_edge_similarity(self, edge1: np.ndarray, edge2: np.ndarray) -> float:
        """Calculate edge feature similarity"""
        return cosine_similarity([edge1], [edge2])[0][0]
    
    def match_multiple_descriptors(self, desc1: np.ndarray, desc2: np.ndarray,
                                  descriptor_type: str = 'sift') -> float:
        """Enhanced descriptor matching with multiple algorithms"""
        if desc1 is None or desc2 is None or len(desc1) == 0 or len(desc2) == 0:
            return 0.0

        try:
            if descriptor_type.lower() == 'sift':
                # FLANN matcher for SIFT
                FLANN_INDEX_KDTREE = 1
                index_params = dict(algorithm=FLANN_INDEX_KDTREE, trees=5)
                search_params = dict(checks=50)
                flann = cv2.FlannBasedMatcher(index_params, search_params)
                matches = flann.knnMatch(desc1, desc2, k=2)

                # Apply Lowe's ratio test
                good_matches = []
                for match_pair in matches:
                    if len(match_pair) == 2:
                        m, n = match_pair
                        if m.distance < self.sift_match_threshold * n.distance:
                            good_matches.append(m)

                return len(good_matches) / max(len(desc1), len(desc2))

            else:
                # BFMatcher for binary descriptors (ORB, AKAZE, BRISK)
                bf = cv2.BFMatcher(cv2.NORM_HAMMING, crossCheck=True)
                matches = bf.match(desc1, desc2)

                # Filter matches by distance
                good_matches = [m for m in matches if m.distance < 50]
                return len(good_matches) / max(len(desc1), len(desc2))

        except Exception as e:
            print(f"Descriptor matching failed: {e}")
            return 0.0

    def calculate_hog_similarity(self, hog1: np.ndarray, hog2: np.ndarray) -> float:
        """Calculate HOG feature similarity"""
        if hog1 is None or hog2 is None or len(hog1) == 0 or len(hog2) == 0:
            return 0.0
        return cosine_similarity([hog1], [hog2])[0][0]

    def comprehensive_similarity(self, features1: Dict, features2: Dict) -> Dict:
        """Calculate comprehensive similarity score with detailed breakdown"""
        similarity_scores = {}

        # SIFT similarity
        sift_score = self.match_multiple_descriptors(
            features1.get('sift_descriptors'),
            features2.get('sift_descriptors'),
            'sift'
        )
        similarity_scores['sift'] = sift_score

        # ORB similarity
        orb_score = self.match_multiple_descriptors(
            features1.get('orb_descriptors'),
            features2.get('orb_descriptors'),
            'orb'
        )
        similarity_scores['orb'] = orb_score

        # AKAZE similarity
        akaze_score = self.match_multiple_descriptors(
            features1.get('akaze_descriptors'),
            features2.get('akaze_descriptors'),
            'akaze'
        )
        similarity_scores['akaze'] = akaze_score

        # BRISK similarity
        brisk_score = self.match_multiple_descriptors(
            features1.get('brisk_descriptors'),
            features2.get('brisk_descriptors'),
            'brisk'
        )
        similarity_scores['brisk'] = brisk_score

        # HOG similarity
        hog_score = self.calculate_hog_similarity(
            features1.get('hog'),
            features2.get('hog')
        )
        similarity_scores['hog'] = hog_score

        # Texture similarity
        texture_score = 0.0
        if 'texture' in features1 and 'texture' in features2:
            texture_score = self.calculate_texture_similarity(
                features1['texture'], features2['texture']
            )
        similarity_scores['texture'] = texture_score

        # Edge similarity
        edge_score = 0.0
        if 'edge' in features1 and 'edge' in features2:
            edge_score = self.calculate_edge_similarity(
                features1['edge'], features2['edge']
            )
        similarity_scores['edge'] = edge_score

        # Polar texture similarity (if available)
        polar_score = 0.0
        if 'polar_texture' in features1 and 'polar_texture' in features2:
            polar_score = self.calculate_texture_similarity(
                features1['polar_texture'], features2['polar_texture']
            )
        similarity_scores['polar_texture'] = polar_score

        # Text similarity
        text_score = 0.0
        if 'text_strings' in features1 and 'text_strings' in features2:
            text_score = self.feature_extractor.text_recognizer.compare_text_lists(
                features1['text_strings'], features2['text_strings']
            )
        similarity_scores['text'] = text_score

        # Calculate weighted overall similarity
        weights = {
            'sift': 0.25,
            'orb': 0.15,
            'akaze': 0.10,
            'brisk': 0.10,
            'hog': 0.10,
            'texture': 0.15,
            'edge': 0.10,
            'polar_texture': 0.03,
            'text': 0.02
        }

        # Calculate weighted average
        total_score = 0.0
        total_weight = 0.0

        for feature, score in similarity_scores.items():
            if score > 0:  # Only include features that have valid scores
                weight = weights.get(feature, 0.0)
                total_score += score * weight
                total_weight += weight

        overall_similarity = total_score / total_weight if total_weight > 0 else 0.0
        similarity_scores['overall'] = overall_similarity

        return similarity_scores
    
    def detect_forgery(self, input_image_path: str) -> List[SealMatch]:
        """Enhanced forgery detection with comprehensive analysis"""
        # Load and preprocess input image
        input_image = cv2.imread(input_image_path)
        if input_image is None:
            raise ValueError(f"Could not load image: {input_image_path}")

        processed_input = self.preprocessor.preprocess_seal(input_image)
        input_features = self.feature_extractor.extract_comprehensive_features(processed_input)

        # Get all authentic seals from database
        authentic_seals = self.database.get_all_seals()

        if not authentic_seals:
            raise ValueError("No authentic seals found in database. Please add some authentic seals first.")

        matches = []

        for seal in authentic_seals:
            try:
                # Load authentic seal features
                authentic_features = self.database.load_features(seal['features_path'])

                # Calculate comprehensive similarity
                similarity_breakdown = self.comprehensive_similarity(input_features, authentic_features)
                overall_similarity = similarity_breakdown['overall']

                # Enhanced authenticity determination using multiple criteria
                authenticity_indicators = []

                # Primary indicator: overall similarity
                if overall_similarity >= self.authenticity_threshold:
                    authenticity_indicators.append(True)
                else:
                    authenticity_indicators.append(False)

                # Secondary indicators: individual feature thresholds
                if similarity_breakdown['sift'] >= 0.3:
                    authenticity_indicators.append(True)

                if similarity_breakdown['texture'] >= 0.6:
                    authenticity_indicators.append(True)

                if similarity_breakdown['text'] >= 0.8:
                    authenticity_indicators.append(True)

                # Determine final authenticity (majority vote with primary weight)
                primary_weight = 2
                total_votes = sum(authenticity_indicators) + (authenticity_indicators[0] * (primary_weight - 1))
                total_possible = len(authenticity_indicators) + (primary_weight - 1)

                is_authentic = (total_votes / total_possible) >= 0.5

                # Calculate confidence based on consistency of indicators
                confidence_base = overall_similarity
                consistency_bonus = sum(authenticity_indicators) / len(authenticity_indicators) * 0.2
                confidence = min(confidence_base + consistency_bonus, 1.0)

                match = SealMatch(
                    template_id=seal['id'],
                    similarity_score=overall_similarity,
                    template_path=seal['image_path'],
                    is_authentic=is_authentic,
                    confidence=confidence
                )

                matches.append(match)

            except Exception as e:
                print(f"Error processing seal {seal['id']}: {e}")
                continue

        # Sort by similarity score and return top 3
        matches.sort(key=lambda x: x.similarity_score, reverse=True)
        top_matches = matches[:3]

        # Record detection in history if we have results
        if top_matches:
            self.database.record_detection(input_image_path, top_matches[0])

        return top_matches

def main():
    """Example usage of the seal forgery detection system"""
    # Initialize system
    database = SealDatabase()
    detector = ForgeryDetector(database)
    
    # Example: Add authentic seals to database (you would do this once)
    # This is just a demonstration - replace with actual authentic seal images
    """
    authentic_seal_paths = [
        "authentic_seals/seal1.jpg",
        "authentic_seals/seal2.jpg",
        "authentic_seals/seal3.jpg"
    ]
    
    for i, path in enumerate(authentic_seal_paths):
        if os.path.exists(path):
            image = cv2.imread(path)
            processed = detector.preprocessor.preprocess_seal(image)
            features = detector.feature_extractor.extract_comprehensive_features(processed)
            
            database.add_seal(
                seal_id=f"authentic_{i+1}",
                name=f"Authentic Seal {i+1}",
                category="official",
                image_path=path,
                features=features
            )
    """
    
    # Test forgery detection
    test_image_path = "test_seal.jpg"
    
    if os.path.exists(test_image_path):
        try:
            results = detector.detect_forgery(test_image_path)
            
            print("Seal Forgery Detection Results:")
            print("=" * 50)
            
            for i, match in enumerate(results, 1):
                print(f"\nRank {i}:")
                print(f"Template ID: {match.template_id}")
                print(f"Similarity Score: {match.similarity_score:.3f}")
                print(f"Authenticity: {'AUTHENTIC' if match.is_authentic else 'POTENTIAL FORGERY'}")
                print(f"Confidence: {match.confidence:.3f}")
                print(f"Template Path: {match.template_path}")
            
            # Overall assessment
            if results and results[0].is_authentic:
                print(f"\n🟢 VERDICT: Seal appears AUTHENTIC (confidence: {results[0].confidence:.1%})")
            else:
                print(f"\n🔴 VERDICT: Seal appears to be a FORGERY (confidence: {1-results[0].confidence if results else 0:.1%})")
                
        except Exception as e:
            print(f"Error during detection: {e}")
    else:
        print(f"Test image not found: {test_image_path}")

if __name__ == "__main__":
    main()