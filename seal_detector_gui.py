import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import cv2
import numpy as np
from PIL import Image, ImageTk
import threading
from seal_forgery_detector import SealDatabase, ForgeryDetector

class SealDetectorGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("Seal Forgery Detection System")
        self.root.geometry("1200x800")
        
        # Initialize detector
        self.database = SealDatabase()
        self.detector = ForgeryDetector(self.database)
        
        self.setup_ui()
        
    def setup_ui(self):
        # Main frame
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Configure grid weights
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(2, weight=1)
        
        # Title
        title_label = ttk.Label(main_frame, text="Seal Forgery Detection System", 
                               font=('Arial', 16, 'bold'))
        title_label.grid(row=0, column=0, columnspan=3, pady=(0, 20))
        
        # Input section
        input_frame = ttk.LabelFrame(main_frame, text="Input Image", padding="10")
        input_frame.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(0, 10))
        
        self.input_image_label = ttk.Label(input_frame, text="No image selected")
        self.input_image_label.grid(row=0, column=0, pady=(0, 10))
        
        ttk.Button(input_frame, text="Select Image", 
                  command=self.select_image).grid(row=1, column=0, pady=(0, 10))
        
        ttk.Button(input_frame, text="Detect Forgery", 
                  command=self.detect_forgery).grid(row=2, column=0)
        
        # Results section
        results_frame = ttk.LabelFrame(main_frame, text="Detection Results", padding="10")
        results_frame.grid(row=1, column=1, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S))
        results_frame.columnconfigure(0, weight=1)
        results_frame.rowconfigure(1, weight=1)
        
        # Verdict label
        self.verdict_label = ttk.Label(results_frame, text="No analysis performed", 
                                      font=('Arial', 12, 'bold'))
        self.verdict_label.grid(row=0, column=0, pady=(0, 10))
        
        # Results tree
        columns = ('Rank', 'Template ID', 'Similarity', 'Status', 'Confidence')
        self.results_tree = ttk.Treeview(results_frame, columns=columns, show='headings', height=8)
        
        for col in columns:
            self.results_tree.heading(col, text=col)
            self.results_tree.column(col, width=100)
        
        self.results_tree.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Scrollbar for results
        scrollbar = ttk.Scrollbar(results_frame, orient=tk.VERTICAL, command=self.results_tree.yview)
        scrollbar.grid(row=1, column=1, sticky=(tk.N, tk.S))
        self.results_tree.configure(yscrollcommand=scrollbar.set)
        
        # Progress bar
        self.progress = ttk.Progressbar(main_frame, mode='indeterminate')
        self.progress.grid(row=2, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(20, 0))
        
        # Status label
        self.status_label = ttk.Label(main_frame, text="Ready")
        self.status_label.grid(row=3, column=0, columnspan=3, pady=(10, 0))
        
        # Database management section
        db_frame = ttk.LabelFrame(main_frame, text="Database Management", padding="10")
        db_frame.grid(row=4, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(20, 0))
        
        ttk.Button(db_frame, text="Add Authentic Seal", 
                  command=self.add_authentic_seal).grid(row=0, column=0, padx=(0, 10))
        
        ttk.Button(db_frame, text="View Database", 
                  command=self.view_database).grid(row=0, column=1, padx=(0, 10))
        
        self.selected_image_path = None
        
    def select_image(self):
        file_path = filedialog.askopenfilename(
            title="Select Seal Image",
            filetypes=[("Image files", "*.jpg *.jpeg *.png *.bmp *.tiff")]
        )
        
        if file_path:
            self.selected_image_path = file_path
            
            # Display image preview
            try:
                image = Image.open(file_path)
                image.thumbnail((200, 200))
                photo = ImageTk.PhotoImage(image)
                
                self.input_image_label.configure(image=photo, text="")
                self.input_image_label.image = photo  # Keep a reference
                
                self.status_label.configure(text=f"Image loaded: {file_path}")
                
            except Exception as e:
                messagebox.showerror("Error", f"Failed to load image: {e}")
    
    def detect_forgery(self):
        if not self.selected_image_path:
            messagebox.showwarning("Warning", "Please select an image first")
            return
        
        # Start detection in separate thread
        threading.Thread(target=self._detect_forgery_thread, daemon=True).start()
    
    def _detect_forgery_thread(self):
        try:
            # Update UI
            self.root.after(0, self._start_detection)
            
            # Perform detection
            results = self.detector.detect_forgery(self.selected_image_path)
            
            # Update UI with results
            self.root.after(0, self._update_results, results)
            
        except Exception as e:
            self.root.after(0, self._detection_error, str(e))
    
    def _start_detection(self):
        self.progress.start()
        self.status_label.configure(text="Analyzing seal...")
        
        # Clear previous results
        for item in self.results_tree.get_children():
            self.results_tree.delete(item)
    
    def _update_results(self, results):
        self.progress.stop()
        
        # Update verdict
        if results and results[0].is_authentic:
            verdict = f"🟢 AUTHENTIC (Confidence: {results[0].confidence:.1%})"
            self.verdict_label.configure(text=verdict, foreground="green")
        else:
            confidence = 1 - results[0].confidence if results else 0
            verdict = f"🔴 POTENTIAL FORGERY (Confidence: {confidence:.1%})"
            self.verdict_label.configure(text=verdict, foreground="red")
        
        # Populate results tree
        for i, match in enumerate(results, 1):
            status = "AUTHENTIC" if match.is_authentic else "FORGERY"
            self.results_tree.insert('', 'end', values=(
                i,
                match.template_id,
                f"{match.similarity_score:.3f}",
                status,
                f"{match.confidence:.1%}"
            ))
        
        self.status_label.configure(text="Detection completed")
    
    def _detection_error(self, error_msg):
        self.progress.stop()
        self.status_label.configure(text="Detection failed")
        messagebox.showerror("Detection Error", f"Failed to detect forgery: {error_msg}")
    
    def add_authentic_seal(self):
        file_path = filedialog.askopenfilename(
            title="Select Authentic Seal Image",
            filetypes=[("Image files", "*.jpg *.jpeg *.png *.bmp *.tiff")]
        )
        
        if file_path:
            # Simple dialog for seal information
            dialog = SealInfoDialog(self.root)
            if dialog.result:
                try:
                    # Process and add to database
                    image = cv2.imread(file_path)
                    processed = self.detector.preprocessor.preprocess_seal(image)
                    features = self.detector.feature_extractor.extract_comprehensive_features(processed)
                    
                    self.database.add_seal(
                        seal_id=dialog.result['id'],
                        name=dialog.result['name'],
                        category=dialog.result['category'],
                        image_path=file_path,
                        features=features
                    )
                    
                    messagebox.showinfo("Success", "Authentic seal added to database")
                    
                except Exception as e:
                    messagebox.showerror("Error", f"Failed to add seal: {e}")
    
    def view_database(self):
        seals = self.database.get_all_seals()
        
        # Create database viewer window
        db_window = tk.Toplevel(self.root)
        db_window.title("Seal Database")
        db_window.geometry("800x400")
        
        # Database tree
        columns = ('ID', 'Name', 'Category', 'Created')
        db_tree = ttk.Treeview(db_window, columns=columns, show='headings')
        
        for col in columns:
            db_tree.heading(col, text=col)
            db_tree.column(col, width=150)
        
        for seal in seals:
            db_tree.insert('', 'end', values=(
                seal['id'],
                seal['name'],
                seal['category'],
                seal['created_at']
            ))
        
        db_tree.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

class SealInfoDialog:
    def __init__(self, parent):
        self.result = None
        
        self.dialog = tk.Toplevel(parent)
        self.dialog.title("Seal Information")
        self.dialog.geometry("300x200")
        self.dialog.transient(parent)
        self.dialog.grab_set()
        
        # Center the dialog
        self.dialog.geometry("+%d+%d" % (parent.winfo_rootx() + 50, parent.winfo_rooty() + 50))
        
        # Form fields
        ttk.Label(self.dialog, text="Seal ID:").grid(row=0, column=0, padx=10, pady=5, sticky=tk.W)
        self.id_entry = ttk.Entry(self.dialog, width=30)
        self.id_entry.grid(row=0, column=1, padx=10, pady=5)
        
        ttk.Label(self.dialog, text="Name:").grid(row=1, column=0, padx=10, pady=5, sticky=tk.W)
        self.name_entry = ttk.Entry(self.dialog, width=30)
        self.name_entry.grid(row=1, column=1, padx=10, pady=5)
        
        ttk.Label(self.dialog, text="Category:").grid(row=2, column=0, padx=10, pady=5, sticky=tk.W)
        self.category_entry = ttk.Entry(self.dialog, width=30)
        self.category_entry.grid(row=2, column=1, padx=10, pady=5)
        
        # Buttons
        button_frame = ttk.Frame(self.dialog)
        button_frame.grid(row=3, column=0, columnspan=2, pady=20)
        
        ttk.Button(button_frame, text="OK", command=self.ok_clicked).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="Cancel", command=self.cancel_clicked).pack(side=tk.LEFT, padx=5)
        
        # Focus on first entry
        self.id_entry.focus()
        
        # Wait for dialog to close
        self.dialog.wait_window()
    
    def ok_clicked(self):
        if self.id_entry.get() and self.name_entry.get():
            self.result = {
                'id': self.id_entry.get(),
                'name': self.name_entry.get(),
                'category': self.category_entry.get() or 'general'
            }
            self.dialog.destroy()
        else:
            messagebox.showwarning("Warning", "Please fill in ID and Name fields")
    
    def cancel_clicked(self):
        self.dialog.destroy()

def main():
    root = tk.Tk()
    app = SealDetectorGUI(root)
    root.mainloop()

if __name__ == "__main__":
    main()