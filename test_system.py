#!/usr/bin/env python3
"""
System Test Script for Advanced Seal Forgery Detection System

This script performs basic system tests to ensure all components are working correctly.
"""

import os
import sys
import cv2
import numpy as np
import tempfile
from pathlib import Path

def test_imports():
    """Test if all required modules can be imported"""
    print("Testing imports...")
    
    try:
        from seal_forgery_detector import (
            SealDatabase, ForgeryDetector, SealPreprocessor, 
            AdvancedFeatureExtractor, TextRecognizer
        )
        print("✓ Core modules imported successfully")
        
        from seal_evaluation import SealEvaluator
        print("✓ Evaluation module imported successfully")
        
        # Test optional imports
        try:
            import PyQt5
            print("✓ PyQt5 available")
        except ImportError:
            print("⚠ PyQt5 not available - GUI will not work")
        
        try:
            from paddleocr import PaddleOCR
            print("✓ PaddleOCR available")
        except ImportError:
            print("⚠ PaddleOCR not available - advanced text recognition disabled")
        
        try:
            import pytesseract
            print("✓ Tesseract available")
        except ImportError:
            print("⚠ Tesseract not available - fallback text recognition disabled")
        
        return True
        
    except ImportError as e:
        print(f"✗ Import failed: {e}")
        return False

def create_test_image():
    """Create a simple test seal image"""
    # Create a red circular seal-like image
    image = np.zeros((200, 200, 3), dtype=np.uint8)
    
    # Draw red circle
    cv2.circle(image, (100, 100), 80, (0, 0, 255), 3)
    
    # Add some text-like patterns
    cv2.putText(image, "TEST", (70, 105), cv2.FONT_HERSHEY_SIMPLEX, 0.8, (0, 0, 255), 2)
    
    # Add some noise and texture
    noise = np.random.randint(0, 50, (200, 200, 3), dtype=np.uint8)
    image = cv2.add(image, noise)
    
    return image

def test_preprocessing():
    """Test the preprocessing pipeline"""
    print("\\nTesting preprocessing...")
    
    try:
        from seal_forgery_detector import SealPreprocessor
        
        preprocessor = SealPreprocessor()
        
        # Create test image
        test_image = create_test_image()
        
        # Test preprocessing
        processed = preprocessor.preprocess_seal(test_image)
        
        # Check results
        required_keys = ['original', 'red_mask', 'gray', 'masked_gray', 'binary']
        for key in required_keys:
            if key not in processed:
                print(f"✗ Missing preprocessing result: {key}")
                return False
        
        print("✓ Preprocessing pipeline working")
        
        # Test circle detection
        if processed.get('circle') is not None:
            print("✓ Circle detection working")
        else:
            print("⚠ Circle detection failed (may be normal for test image)")
        
        return True
        
    except Exception as e:
        print(f"✗ Preprocessing failed: {e}")
        return False

def test_feature_extraction():
    """Test feature extraction"""
    print("\\nTesting feature extraction...")
    
    try:
        from seal_forgery_detector import SealPreprocessor, AdvancedFeatureExtractor
        
        preprocessor = SealPreprocessor()
        extractor = AdvancedFeatureExtractor()
        
        # Create and preprocess test image
        test_image = create_test_image()
        processed = preprocessor.preprocess_seal(test_image)
        
        # Extract features
        features = extractor.extract_comprehensive_features(processed)
        
        # Check feature types
        expected_features = ['sift_descriptors', 'orb_descriptors', 'texture', 'edge', 'hog']
        
        for feature_type in expected_features:
            if feature_type in features and features[feature_type] is not None:
                print(f"✓ {feature_type} extraction working")
            else:
                print(f"⚠ {feature_type} extraction failed or returned None")
        
        # Check text features
        if 'text_info' in features:
            print("✓ Text recognition integration working")
        else:
            print("⚠ Text recognition not available")
        
        return True
        
    except Exception as e:
        print(f"✗ Feature extraction failed: {e}")
        return False

def test_database():
    """Test database operations"""
    print("\\nTesting database...")
    
    try:
        from seal_forgery_detector import SealDatabase
        
        # Create temporary database
        with tempfile.NamedTemporaryFile(suffix='.db', delete=False) as tmp_db:
            db_path = tmp_db.name
        
        database = SealDatabase(db_path)
        
        # Test database initialization
        seals = database.get_all_seals()
        print(f"✓ Database initialized, contains {len(seals)} seals")
        
        # Test categories
        categories = database.get_categories()
        print(f"✓ Categories loaded: {len(categories)} categories")
        
        # Clean up
        os.unlink(db_path)
        
        return True
        
    except Exception as e:
        print(f"✗ Database test failed: {e}")
        return False

def test_detection_system():
    """Test the complete detection system"""
    print("\\nTesting detection system...")
    
    try:
        from seal_forgery_detector import SealDatabase, ForgeryDetector, SealPreprocessor, AdvancedFeatureExtractor
        
        # Create temporary database
        with tempfile.NamedTemporaryFile(suffix='.db', delete=False) as tmp_db:
            db_path = tmp_db.name
        
        database = SealDatabase(db_path)
        detector = ForgeryDetector(database)
        
        # Create test images
        test_image1 = create_test_image()
        test_image2 = create_test_image()
        
        # Save test images temporarily
        with tempfile.NamedTemporaryFile(suffix='.jpg', delete=False) as tmp1:
            cv2.imwrite(tmp1.name, test_image1)
            image1_path = tmp1.name
        
        with tempfile.NamedTemporaryFile(suffix='.jpg', delete=False) as tmp2:
            cv2.imwrite(tmp2.name, test_image2)
            image2_path = tmp2.name
        
        try:
            # Add first image as authentic seal
            processed = detector.preprocessor.preprocess_seal(test_image1)
            features = detector.feature_extractor.extract_comprehensive_features(processed)
            
            database.add_seal(
                seal_id="test_001",
                name="Test Seal",
                category="test",
                image_path=image1_path,
                features=features,
                description="Test seal for system validation"
            )
            
            print("✓ Authentic seal added to database")
            
            # Test detection on second image
            results = detector.detect_forgery(image2_path)
            
            if results:
                print(f"✓ Detection completed, found {len(results)} matches")
                best_match = results[0]
                print(f"  Best match: {best_match.template_id}")
                print(f"  Similarity: {best_match.similarity_score:.3f}")
                print(f"  Status: {'AUTHENTIC' if best_match.is_authentic else 'FORGERY'}")
            else:
                print("⚠ Detection completed but no matches found")
            
            return True
            
        finally:
            # Clean up temporary files
            os.unlink(image1_path)
            os.unlink(image2_path)
            os.unlink(db_path)
        
    except Exception as e:
        print(f"✗ Detection system test failed: {e}")
        return False

def test_gui_availability():
    """Test if GUI components are available"""
    print("\\nTesting GUI availability...")
    
    try:
        import PyQt5
        from PyQt5.QtWidgets import QApplication
        
        # Test if we can create a QApplication (basic GUI test)
        app = QApplication.instance()
        if app is None:
            app = QApplication([])
        
        print("✓ PyQt5 GUI components available")
        
        # Test if our GUI module can be imported
        try:
            import seal_detector_pyqt5_gui
            print("✓ Custom GUI module available")
        except ImportError as e:
            print(f"⚠ Custom GUI module import failed: {e}")
        
        return True
        
    except ImportError:
        print("⚠ PyQt5 not available - GUI functionality disabled")
        return False

def test_evaluation_system():
    """Test evaluation system"""
    print("\\nTesting evaluation system...")
    
    try:
        from seal_evaluation import SealEvaluator
        from seal_forgery_detector import SealDatabase, ForgeryDetector
        
        # Create temporary database
        with tempfile.NamedTemporaryFile(suffix='.db', delete=False) as tmp_db:
            db_path = tmp_db.name
        
        database = SealDatabase(db_path)
        detector = ForgeryDetector(database)
        evaluator = SealEvaluator(detector)
        
        print("✓ Evaluation system initialized")
        
        # Test metrics calculation with dummy data
        dummy_results = {
            'predictions': [1, 0, 1, 0, 1],
            'ground_truth': [1, 0, 0, 0, 1],
            'authentic': [],
            'forged': []
        }
        
        metrics = evaluator.calculate_metrics(dummy_results)
        if metrics:
            print("✓ Metrics calculation working")
            print(f"  Sample accuracy: {metrics['accuracy']:.3f}")
        
        # Clean up
        os.unlink(db_path)
        
        return True
        
    except Exception as e:
        print(f"✗ Evaluation system test failed: {e}")
        return False

def run_all_tests():
    """Run all system tests"""
    print("Advanced Seal Forgery Detection System - System Tests")
    print("=" * 60)
    
    tests = [
        ("Import Test", test_imports),
        ("Preprocessing Test", test_preprocessing),
        ("Feature Extraction Test", test_feature_extraction),
        ("Database Test", test_database),
        ("Detection System Test", test_detection_system),
        ("GUI Availability Test", test_gui_availability),
        ("Evaluation System Test", test_evaluation_system),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\\n{'='*20} {test_name} {'='*20}")
        try:
            if test_func():
                passed += 1
        except Exception as e:
            print(f"✗ {test_name} failed with exception: {e}")
    
    print("\\n" + "=" * 60)
    print(f"Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! System is ready to use.")
        return True
    else:
        print("⚠ Some tests failed. Check the output above for details.")
        return False

def main():
    """Main test function"""
    success = run_all_tests()
    
    if success:
        print("\\nNext steps:")
        print("1. Run 'python seal_detector_pyqt5_gui.py' to start the GUI")
        print("2. Run 'python usage_examples.py' to see usage examples")
        print("3. Add authentic seals to the database before testing")
    else:
        print("\\nPlease fix the failing tests before using the system.")
    
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())
