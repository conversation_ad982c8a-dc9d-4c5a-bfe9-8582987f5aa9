import sys
import os
from PyQt5.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout, 
                             QHBoxLayout, QGridLayout, QLabel, QPushButton, 
                             QFileDialog, QTableWidget, QTableWidgetItem, 
                             QProgressBar, QTextEdit, QGroupBox, QSplitter,
                             QMessageBox, QDialog, QFormLayout, QLineEdit,
                             QComboBox, QTabWidget, QScrollArea, QFrame)
from PyQt5.QtCore import Qt, QThread, pyqtSignal, QTimer
from PyQt5.QtGui import QPixmap, QFont, QIcon, QPalette, QColor
import cv2
import numpy as np
from PIL import Image
from seal_forgery_detector import SealDatabase, ForgeryDetector

class DetectionWorker(QThread):
    """Worker thread for seal forgery detection"""
    finished = pyqtSignal(list)
    error = pyqtSignal(str)
    progress = pyqtSignal(str)
    
    def __init__(self, detector, image_path):
        super().__init__()
        self.detector = detector
        self.image_path = image_path
    
    def run(self):
        try:
            self.progress.emit("Loading and preprocessing image...")
            results = self.detector.detect_forgery(self.image_path)
            self.progress.emit("Analysis complete")
            self.finished.emit(results)
        except Exception as e:
            self.error.emit(str(e))

class SealInfoDialog(QDialog):
    """Dialog for entering seal information"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("Add Authentic Seal")
        self.setModal(True)
        self.setFixedSize(400, 300)
        
        self.result = None
        self.setup_ui()
    
    def setup_ui(self):
        layout = QFormLayout()
        
        # Form fields
        self.id_edit = QLineEdit()
        self.name_edit = QLineEdit()
        self.category_combo = QComboBox()
        self.category_combo.addItems(['government', 'corporate', 'personal', 'academic', 'other'])
        self.description_edit = QTextEdit()
        self.description_edit.setMaximumHeight(80)
        
        layout.addRow("Seal ID:", self.id_edit)
        layout.addRow("Name:", self.name_edit)
        layout.addRow("Category:", self.category_combo)
        layout.addRow("Description:", self.description_edit)
        
        # Buttons
        button_layout = QHBoxLayout()
        ok_button = QPushButton("OK")
        cancel_button = QPushButton("Cancel")
        
        ok_button.clicked.connect(self.accept_dialog)
        cancel_button.clicked.connect(self.reject)
        
        button_layout.addWidget(ok_button)
        button_layout.addWidget(cancel_button)
        
        main_layout = QVBoxLayout()
        main_layout.addLayout(layout)
        main_layout.addLayout(button_layout)
        
        self.setLayout(main_layout)
    
    def accept_dialog(self):
        if self.id_edit.text() and self.name_edit.text():
            self.result = {
                'id': self.id_edit.text(),
                'name': self.name_edit.text(),
                'category': self.category_combo.currentText(),
                'description': self.description_edit.toPlainText()
            }
            self.accept()
        else:
            QMessageBox.warning(self, "Warning", "Please fill in ID and Name fields")

class ImageDisplayWidget(QLabel):
    """Custom widget for displaying images with scaling"""
    
    def __init__(self):
        super().__init__()
        self.setMinimumSize(300, 300)
        self.setStyleSheet("border: 2px dashed #aaa; background-color: #f9f9f9;")
        self.setAlignment(Qt.AlignCenter)
        self.setText("No image selected\nClick 'Select Image' to load")
        self.setScaledContents(False)
    
    def set_image(self, image_path):
        """Set and display image with proper scaling"""
        try:
            pixmap = QPixmap(image_path)
            if not pixmap.isNull():
                # Scale image to fit widget while maintaining aspect ratio
                scaled_pixmap = pixmap.scaled(
                    self.size(), Qt.KeepAspectRatio, Qt.SmoothTransformation
                )
                self.setPixmap(scaled_pixmap)
                self.setText("")
            else:
                self.setText("Failed to load image")
        except Exception as e:
            self.setText(f"Error loading image:\n{str(e)}")

class SealDetectorMainWindow(QMainWindow):
    """Main application window with PyQt5 interface"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Advanced Seal Forgery Detection System")
        self.setGeometry(100, 100, 1400, 900)
        
        # Initialize detector
        self.database = SealDatabase()
        self.detector = ForgeryDetector(self.database)
        self.selected_image_path = None
        self.detection_worker = None
        
        self.setup_ui()
        self.setup_styles()
    
    def setup_ui(self):
        """Setup the main user interface"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # Main layout
        main_layout = QHBoxLayout(central_widget)
        
        # Create splitter for resizable panels
        splitter = QSplitter(Qt.Horizontal)
        main_layout.addWidget(splitter)
        
        # Left panel - Input and controls
        left_panel = self.create_left_panel()
        splitter.addWidget(left_panel)
        
        # Right panel - Results and analysis
        right_panel = self.create_right_panel()
        splitter.addWidget(right_panel)
        
        # Set splitter proportions
        splitter.setSizes([500, 900])
        
        # Status bar
        self.statusBar().showMessage("Ready")
        
        # Progress bar in status bar
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        self.statusBar().addPermanentWidget(self.progress_bar)
    
    def create_left_panel(self):
        """Create the left control panel"""
        panel = QWidget()
        layout = QVBoxLayout(panel)
        
        # Title
        title = QLabel("Seal Forgery Detection")
        title.setFont(QFont("Arial", 16, QFont.Bold))
        title.setAlignment(Qt.AlignCenter)
        layout.addWidget(title)
        
        # Image input group
        input_group = QGroupBox("Input Image")
        input_layout = QVBoxLayout(input_group)
        
        # Image display
        self.image_display = ImageDisplayWidget()
        input_layout.addWidget(self.image_display)
        
        # Control buttons
        button_layout = QVBoxLayout()
        
        self.select_button = QPushButton("Select Image")
        self.select_button.clicked.connect(self.select_image)
        button_layout.addWidget(self.select_button)
        
        self.detect_button = QPushButton("Detect Forgery")
        self.detect_button.clicked.connect(self.detect_forgery)
        self.detect_button.setEnabled(False)
        button_layout.addWidget(self.detect_button)
        
        input_layout.addLayout(button_layout)
        layout.addWidget(input_group)
        
        # Database management group
        db_group = QGroupBox("Database Management")
        db_layout = QVBoxLayout(db_group)
        
        add_seal_button = QPushButton("Add Authentic Seal")
        add_seal_button.clicked.connect(self.add_authentic_seal)
        db_layout.addWidget(add_seal_button)
        
        view_db_button = QPushButton("View Database")
        view_db_button.clicked.connect(self.view_database)
        db_layout.addWidget(view_db_button)
        
        layout.addWidget(db_group)
        
        # Add stretch to push everything to top
        layout.addStretch()
        
        return panel
    
    def create_right_panel(self):
        """Create the right results panel"""
        panel = QWidget()
        layout = QVBoxLayout(panel)
        
        # Results tabs
        self.results_tabs = QTabWidget()
        
        # Detection results tab
        results_tab = self.create_results_tab()
        self.results_tabs.addTab(results_tab, "Detection Results")
        
        # Analysis details tab
        analysis_tab = self.create_analysis_tab()
        self.results_tabs.addTab(analysis_tab, "Analysis Details")
        
        layout.addWidget(self.results_tabs)
        
        return panel
    
    def create_results_tab(self):
        """Create the detection results tab"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # Verdict display
        self.verdict_label = QLabel("No analysis performed")
        self.verdict_label.setFont(QFont("Arial", 14, QFont.Bold))
        self.verdict_label.setAlignment(Qt.AlignCenter)
        self.verdict_label.setStyleSheet("padding: 10px; border: 2px solid #ddd; border-radius: 5px;")
        layout.addWidget(self.verdict_label)
        
        # Results table
        self.results_table = QTableWidget()
        self.results_table.setColumnCount(5)
        self.results_table.setHorizontalHeaderLabels([
            'Rank', 'Template ID', 'Similarity', 'Status', 'Confidence'
        ])
        self.results_table.horizontalHeader().setStretchLastSection(True)
        layout.addWidget(self.results_table)
        
        return tab
    
    def create_analysis_tab(self):
        """Create the analysis details tab"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # Analysis log
        self.analysis_log = QTextEdit()
        self.analysis_log.setReadOnly(True)
        self.analysis_log.setFont(QFont("Consolas", 9))
        layout.addWidget(self.analysis_log)
        
        return tab
    
    def setup_styles(self):
        """Setup application styles"""
        self.setStyleSheet("""
            QMainWindow {
                background-color: #f5f5f5;
            }
            QGroupBox {
                font-weight: bold;
                border: 2px solid #cccccc;
                border-radius: 5px;
                margin-top: 1ex;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
            QPushButton {
                background-color: #4CAF50;
                border: none;
                color: white;
                padding: 8px 16px;
                text-align: center;
                font-size: 14px;
                border-radius: 4px;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
            QPushButton:pressed {
                background-color: #3d8b40;
            }
            QPushButton:disabled {
                background-color: #cccccc;
                color: #666666;
            }
            QTableWidget {
                gridline-color: #d0d0d0;
                background-color: white;
            }
            QTableWidget::item {
                padding: 5px;
            }
            QTabWidget::pane {
                border: 1px solid #cccccc;
                background-color: white;
            }
            QTabBar::tab {
                background-color: #e0e0e0;
                padding: 8px 16px;
                margin-right: 2px;
            }
            QTabBar::tab:selected {
                background-color: white;
                border-bottom: 2px solid #4CAF50;
            }
        """)

    def select_image(self):
        """Select and load an image for analysis"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "Select Seal Image", "",
            "Image files (*.jpg *.jpeg *.png *.bmp *.tiff);;All files (*)"
        )

        if file_path:
            self.selected_image_path = file_path
            self.image_display.set_image(file_path)
            self.detect_button.setEnabled(True)
            self.statusBar().showMessage(f"Image loaded: {os.path.basename(file_path)}")
            self.analysis_log.clear()
            self.analysis_log.append(f"Image loaded: {file_path}")

    def detect_forgery(self):
        """Start forgery detection process"""
        if not self.selected_image_path:
            QMessageBox.warning(self, "Warning", "Please select an image first")
            return

        # Disable controls during detection
        self.detect_button.setEnabled(False)
        self.select_button.setEnabled(False)

        # Show progress
        self.progress_bar.setVisible(True)
        self.progress_bar.setRange(0, 0)  # Indeterminate progress

        # Clear previous results
        self.results_table.setRowCount(0)
        self.analysis_log.append("\\nStarting forgery detection analysis...")

        # Start detection in worker thread
        self.detection_worker = DetectionWorker(self.detector, self.selected_image_path)
        self.detection_worker.finished.connect(self.on_detection_finished)
        self.detection_worker.error.connect(self.on_detection_error)
        self.detection_worker.progress.connect(self.on_detection_progress)
        self.detection_worker.start()

    def on_detection_progress(self, message):
        """Handle detection progress updates"""
        self.statusBar().showMessage(message)
        self.analysis_log.append(f"Progress: {message}")

    def on_detection_finished(self, results):
        """Handle completed detection"""
        self.progress_bar.setVisible(False)
        self.detect_button.setEnabled(True)
        self.select_button.setEnabled(True)

        if not results:
            self.verdict_label.setText("No matching templates found in database")
            self.verdict_label.setStyleSheet(
                "padding: 10px; border: 2px solid #ff9800; border-radius: 5px; "
                "background-color: #fff3e0; color: #f57c00;"
            )
            self.statusBar().showMessage("Detection completed - No matches found")
            return

        # Update verdict
        best_match = results[0]
        if best_match.is_authentic:
            verdict_text = f"🟢 AUTHENTIC (Confidence: {best_match.confidence:.1%})"
            verdict_style = (
                "padding: 10px; border: 2px solid #4caf50; border-radius: 5px; "
                "background-color: #e8f5e8; color: #2e7d32;"
            )
        else:
            confidence = 1 - best_match.confidence
            verdict_text = f"🔴 POTENTIAL FORGERY (Confidence: {confidence:.1%})"
            verdict_style = (
                "padding: 10px; border: 2px solid #f44336; border-radius: 5px; "
                "background-color: #ffebee; color: #c62828;"
            )

        self.verdict_label.setText(verdict_text)
        self.verdict_label.setStyleSheet(verdict_style)

        # Populate results table
        self.results_table.setRowCount(len(results))
        for i, match in enumerate(results):
            self.results_table.setItem(i, 0, QTableWidgetItem(str(i + 1)))
            self.results_table.setItem(i, 1, QTableWidgetItem(match.template_id))
            self.results_table.setItem(i, 2, QTableWidgetItem(f"{match.similarity_score:.3f}"))

            status_item = QTableWidgetItem("AUTHENTIC" if match.is_authentic else "FORGERY")
            if match.is_authentic:
                status_item.setBackground(QColor(200, 255, 200))
            else:
                status_item.setBackground(QColor(255, 200, 200))
            self.results_table.setItem(i, 3, status_item)

            self.results_table.setItem(i, 4, QTableWidgetItem(f"{match.confidence:.1%}"))

        # Update analysis log
        self.analysis_log.append(f"\\nDetection completed. Found {len(results)} matches:")
        for i, match in enumerate(results, 1):
            self.analysis_log.append(
                f"{i}. {match.template_id}: {match.similarity_score:.3f} "
                f"({'AUTHENTIC' if match.is_authentic else 'FORGERY'})"
            )

        self.statusBar().showMessage("Detection completed successfully")

    def on_detection_error(self, error_message):
        """Handle detection errors"""
        self.progress_bar.setVisible(False)
        self.detect_button.setEnabled(True)
        self.select_button.setEnabled(True)

        QMessageBox.critical(self, "Detection Error", f"Failed to detect forgery:\\n{error_message}")
        self.analysis_log.append(f"ERROR: {error_message}")
        self.statusBar().showMessage("Detection failed")

    def add_authentic_seal(self):
        """Add a new authentic seal to the database"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "Select Authentic Seal Image", "",
            "Image files (*.jpg *.jpeg *.png *.bmp *.tiff);;All files (*)"
        )

        if not file_path:
            return

        # Get seal information
        dialog = SealInfoDialog(self)
        if dialog.exec_() == QDialog.Accepted and dialog.result:
            try:
                # Process and add to database
                image = cv2.imread(file_path)
                if image is None:
                    QMessageBox.critical(self, "Error", "Failed to load image")
                    return

                processed = self.detector.preprocessor.preprocess_seal(image)
                features = self.detector.feature_extractor.extract_comprehensive_features(processed)

                self.database.add_seal(
                    seal_id=dialog.result['id'],
                    name=dialog.result['name'],
                    category=dialog.result['category'],
                    image_path=file_path,
                    features=features
                )

                QMessageBox.information(self, "Success", "Authentic seal added to database successfully")
                self.analysis_log.append(f"Added authentic seal: {dialog.result['name']} ({dialog.result['id']})")

            except Exception as e:
                QMessageBox.critical(self, "Error", f"Failed to add seal to database:\\n{str(e)}")

    def view_database(self):
        """View all seals in the database"""
        try:
            seals = self.database.get_all_seals()

            if not seals:
                QMessageBox.information(self, "Database", "No seals found in database")
                return

            # Create database viewer dialog
            dialog = DatabaseViewerDialog(seals, self)
            dialog.exec_()

        except Exception as e:
            QMessageBox.critical(self, "Error", f"Failed to load database:\\n{str(e)}")

class DatabaseViewerDialog(QDialog):
    """Dialog for viewing database contents"""

    def __init__(self, seals, parent=None):
        super().__init__(parent)
        self.setWindowTitle("Seal Database")
        self.setGeometry(200, 200, 800, 500)
        self.seals = seals

        self.setup_ui()

    def setup_ui(self):
        layout = QVBoxLayout()

        # Info label
        info_label = QLabel(f"Database contains {len(self.seals)} authentic seals:")
        info_label.setFont(QFont("Arial", 12, QFont.Bold))
        layout.addWidget(info_label)

        # Database table
        table = QTableWidget()
        table.setColumnCount(4)
        table.setHorizontalHeaderLabels(['ID', 'Name', 'Category', 'Created'])
        table.horizontalHeader().setStretchLastSection(True)

        table.setRowCount(len(self.seals))
        for i, seal in enumerate(self.seals):
            table.setItem(i, 0, QTableWidgetItem(seal['id']))
            table.setItem(i, 1, QTableWidgetItem(seal['name']))
            table.setItem(i, 2, QTableWidgetItem(seal['category']))
            table.setItem(i, 3, QTableWidgetItem(seal['created_at']))

        layout.addWidget(table)

        # Close button
        close_button = QPushButton("Close")
        close_button.clicked.connect(self.accept)
        layout.addWidget(close_button)

        self.setLayout(layout)

def main():
    """Main application entry point"""
    app = QApplication(sys.argv)

    # Set application properties
    app.setApplicationName("Seal Forgery Detection System")
    app.setApplicationVersion("2.0")
    app.setOrganizationName("Advanced CV Solutions")

    # Create and show main window
    window = SealDetectorMainWindow()
    window.show()

    # Start event loop
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
