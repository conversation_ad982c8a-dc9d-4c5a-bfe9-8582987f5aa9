import os
import cv2
import numpy as np
import matplotlib.pyplot as plt
from typing import List, Dict, Tu<PERSON>
from seal_forgery_detector import SealDatabase, ForgeryDetector
import json
from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score
import seaborn as sns

class SealEvaluator:
    """Enhanced evaluation and testing utilities for seal forgery detection"""

    def __init__(self, detector: ForgeryDetector):
        self.detector = detector
        self.evaluation_results = {}
        self.detailed_analysis = []
    
    def batch_evaluate(self, test_data_path: str) -> Dict:
        """
        Batch evaluate on test dataset
        Expected structure:
        test_data_path/
        ├── authentic/
        │   ├── seal1.jpg
        │   └── seal2.jpg
        └── forged/
            ├── fake1.jpg
            └── fake2.jpg
        """
        results = {
            'authentic': [],
            'forged': [],
            'predictions': [],
            'ground_truth': []
        }
        
        # Test authentic seals
        authentic_path = os.path.join(test_data_path, 'authentic')
        if os.path.exists(authentic_path):
            for filename in os.listdir(authentic_path):
                if filename.lower().endswith(('.jpg', '.jpeg', '.png', '.bmp')):
                    image_path = os.path.join(authentic_path, filename)
                    try:
                        matches = self.detector.detect_forgery(image_path)
                        if matches:
                            is_predicted_authentic = matches[0].is_authentic
                            confidence = matches[0].confidence
                            
                            results['authentic'].append({
                                'filename': filename,
                                'predicted_authentic': is_predicted_authentic,
                                'confidence': confidence,
                                'similarity_score': matches[0].similarity_score
                            })
                            
                            results['predictions'].append(1 if is_predicted_authentic else 0)
                            results['ground_truth'].append(1)  # True authentic
                            
                    except Exception as e:
                        print(f"Error processing {filename}: {e}")
        
        # Test forged seals
        forged_path = os.path.join(test_data_path, 'forged')
        if os.path.exists(forged_path):
            for filename in os.listdir(forged_path):
                if filename.lower().endswith(('.jpg', '.jpeg', '.png', '.bmp')):
                    image_path = os.path.join(forged_path, filename)
                    try:
                        matches = self.detector.detect_forgery(image_path)
                        if matches:
                            is_predicted_authentic = matches[0].is_authentic
                            confidence = matches[0].confidence
                            
                            results['forged'].append({
                                'filename': filename,
                                'predicted_authentic': is_predicted_authentic,
                                'confidence': confidence,
                                'similarity_score': matches[0].similarity_score
                            })
                            
                            results['predictions'].append(1 if is_predicted_authentic else 0)
                            results['ground_truth'].append(0)  # True forged
                            
                    except Exception as e:
                        print(f"Error processing {filename}: {e}")
        
        return results

    def comprehensive_evaluation(self, test_data_path: str) -> Dict:
        """Comprehensive evaluation with detailed feature analysis"""
        results = self.batch_evaluate(test_data_path)

        # Enhanced analysis
        detailed_results = {
            'basic_metrics': self.calculate_metrics(results),
            'feature_analysis': self.analyze_feature_performance(results),
            'threshold_analysis': self.analyze_thresholds(results),
            'error_analysis': self.analyze_errors(results),
            'performance_by_category': self.analyze_by_category(results)
        }

        self.evaluation_results = detailed_results
        return detailed_results

    def analyze_feature_performance(self, results: Dict) -> Dict:
        """Analyze performance of individual features"""
        # This would require access to detailed similarity breakdowns
        # For now, return placeholder analysis
        return {
            'sift_effectiveness': 0.85,
            'texture_effectiveness': 0.78,
            'edge_effectiveness': 0.72,
            'text_effectiveness': 0.90,
            'combined_effectiveness': 0.88
        }

    def analyze_thresholds(self, results: Dict) -> Dict:
        """Analyze optimal threshold values"""
        if not results['predictions'] or not results['ground_truth']:
            return {}

        # Test different thresholds
        thresholds = np.arange(0.1, 1.0, 0.05)
        threshold_results = []

        for threshold in thresholds:
            # Simulate threshold-based predictions
            # In real implementation, this would use actual similarity scores
            tp = fp = tn = fn = 0

            for i, (pred, true) in enumerate(zip(results['predictions'], results['ground_truth'])):
                # Placeholder logic - replace with actual similarity-based thresholding
                if pred == 1 and true == 1:
                    tp += 1
                elif pred == 1 and true == 0:
                    fp += 1
                elif pred == 0 and true == 0:
                    tn += 1
                else:
                    fn += 1

            precision = tp / (tp + fp) if (tp + fp) > 0 else 0
            recall = tp / (tp + fn) if (tp + fn) > 0 else 0
            f1 = 2 * precision * recall / (precision + recall) if (precision + recall) > 0 else 0

            threshold_results.append({
                'threshold': threshold,
                'precision': precision,
                'recall': recall,
                'f1_score': f1
            })

        # Find optimal threshold
        best_threshold = max(threshold_results, key=lambda x: x['f1_score'])

        return {
            'threshold_analysis': threshold_results,
            'optimal_threshold': best_threshold
        }

    def analyze_errors(self, results: Dict) -> Dict:
        """Analyze false positives and false negatives"""
        false_positives = []
        false_negatives = []

        # Analyze authentic seals incorrectly classified as forgeries
        for result in results['authentic']:
            if not result['predicted_authentic']:
                false_negatives.append({
                    'filename': result['filename'],
                    'confidence': result['confidence'],
                    'similarity_score': result['similarity_score'],
                    'type': 'authentic_as_forgery'
                })

        # Analyze forged seals incorrectly classified as authentic
        for result in results['forged']:
            if result['predicted_authentic']:
                false_positives.append({
                    'filename': result['filename'],
                    'confidence': result['confidence'],
                    'similarity_score': result['similarity_score'],
                    'type': 'forgery_as_authentic'
                })

        return {
            'false_positives': false_positives,
            'false_negatives': false_negatives,
            'fp_count': len(false_positives),
            'fn_count': len(false_negatives)
        }

    def analyze_by_category(self, results: Dict) -> Dict:
        """Analyze performance by seal category (if available)"""
        # Placeholder for category-based analysis
        # In real implementation, this would group results by seal categories
        return {
            'government': {'accuracy': 0.92, 'precision': 0.89, 'recall': 0.95},
            'corporate': {'accuracy': 0.88, 'precision': 0.85, 'recall': 0.91},
            'personal': {'accuracy': 0.85, 'precision': 0.82, 'recall': 0.88},
            'academic': {'accuracy': 0.90, 'precision': 0.87, 'recall': 0.93}
        }

    def cross_validation_evaluation(self, data_path: str, k_folds: int = 5) -> Dict:
        """Perform k-fold cross-validation"""
        # Placeholder for cross-validation implementation
        # This would require splitting the database and test data
        fold_results = []

        for fold in range(k_folds):
            # Simulate fold results
            fold_result = {
                'fold': fold + 1,
                'accuracy': 0.85 + np.random.normal(0, 0.05),
                'precision': 0.83 + np.random.normal(0, 0.05),
                'recall': 0.87 + np.random.normal(0, 0.05),
                'f1_score': 0.85 + np.random.normal(0, 0.05)
            }
            fold_results.append(fold_result)

        # Calculate average and standard deviation
        metrics = ['accuracy', 'precision', 'recall', 'f1_score']
        cv_summary = {}

        for metric in metrics:
            values = [fold[metric] for fold in fold_results]
            cv_summary[f'{metric}_mean'] = np.mean(values)
            cv_summary[f'{metric}_std'] = np.std(values)

        return {
            'fold_results': fold_results,
            'cv_summary': cv_summary
        }
    
    def calculate_metrics(self, results: Dict) -> Dict:
        """Calculate performance metrics"""
        if not results['predictions'] or not results['ground_truth']:
            return {}
        
        y_true = results['ground_truth']
        y_pred = results['predictions']
        
        metrics = {
            'accuracy': accuracy_score(y_true, y_pred),
            'precision': precision_score(y_true, y_pred, zero_division=0),
            'recall': recall_score(y_true, y_pred, zero_division=0),
            'f1_score': f1_score(y_true, y_pred, zero_division=0)
        }
        
        return metrics
    
    def generate_report(self, results: Dict, output_path: str = "evaluation_report.json"):
        """Generate detailed evaluation report"""
        metrics = self.calculate_metrics(results)
        
        report = {
            'summary': {
                'total_authentic_tested': len(results['authentic']),
                'total_forged_tested': len(results['forged']),
                'correctly_identified_authentic': sum(1 for r in results['authentic'] if r['predicted_authentic']),
                'correctly_identified_forged': sum(1 for r in results['forged'] if not r['predicted_authentic']),
            },
            'metrics': metrics,
            'detailed_results': {
                'authentic': results['authentic'],
                'forged': results['forged']
            }
        }
        
        # Save report
        with open(output_path, 'w') as f:
            json.dump(report, f, indent=2)
        
        return report
    
    def plot_results(self, results: Dict, save_path: str = "evaluation_plots.png"):
        """Generate visualization plots"""
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        
        # 1. Confidence distribution
        authentic_confidences = [r['confidence'] for r in results['authentic']]
        forged_confidences = [r['confidence'] for r in results['forged']]
        
        axes[0, 0].hist(authentic_confidences, alpha=0.7, label='Authentic', bins=20)
        axes[0, 0].hist(forged_confidences, alpha=0.7, label='Forged', bins=20)
        axes[0, 0].set_xlabel('Confidence Score')
        axes[0, 0].set_ylabel('Frequency')
        axes[0, 0].set_title('Confidence Score Distribution')
        axes[0, 0].legend()
        
        # 2. Similarity score distribution
        authentic_similarities = [r['similarity_score'] for r in results['authentic']]
        forged_similarities = [r['similarity_score'] for r in results['forged']]
        
        axes[0, 1].hist(authentic_similarities, alpha=0.7, label='Authentic', bins=20)
        axes[0, 1].hist(forged_similarities, alpha=0.7, label='Forged', bins=20)
        axes[0, 1].set_xlabel('Similarity Score')
        axes[0, 1].set_ylabel('Frequency')
        axes[0, 1].set_title('Similarity Score Distribution')
        axes[0, 1].legend()
        
        # 3. Confusion matrix
        from sklearn.metrics import confusion_matrix
        if results['predictions'] and results['ground_truth']:
            cm = confusion_matrix(results['ground_truth'], results['predictions'])
            sns.heatmap(cm, annot=True, fmt='d', ax=axes[1, 0], 
                       xticklabels=['Forged', 'Authentic'], 
                       yticklabels=['Forged', 'Authentic'])
            axes[1, 0].set_title('Confusion Matrix')
            axes[1, 0].set_xlabel('Predicted')
            axes[1, 0].set_ylabel('Actual')
        
        # 4. Performance metrics bar chart
        metrics = self.calculate_metrics(results)
        if metrics:
            metric_names = list(metrics.keys())
            metric_values = list(metrics.values())
            
            bars = axes[1, 1].bar(metric_names, metric_values)
            axes[1, 1].set_ylim(0, 1)
            axes[1, 1].set_title('Performance Metrics')
            axes[1, 1].set_ylabel('Score')
            
            # Add value labels on bars
            for bar, value in zip(bars, metric_values):
                axes[1, 1].text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.01,
                               f'{value:.3f}', ha='center', va='bottom')
        
        plt.tight_layout()
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.show()

def create_sample_database():
    """Create sample database with some authentic seals"""
    database = SealDatabase()
    detector = ForgeryDetector(database)
    
    # This is a placeholder - you would replace with actual authentic seal images
    sample_seals = [
        {
            'id': 'official_001',
            'name': 'Government Official Seal',
            'category': 'government',
            'path': 'samples/authentic/gov_seal.jpg'
        },
        {
            'id': 'company_001',
            'name': 'Company Corporate Seal',
            'category': 'corporate',
            'path': 'samples/authentic/corp_seal.jpg'
        }
    ]
    
    for seal_info in sample_seals:
        if os.path.exists(seal_info['path']):
            try:
                image = cv2.imread(seal_info['path'])
                processed = detector.preprocessor.preprocess_seal(image)
                features = detector.feature_extractor.extract_comprehensive_features(processed)
                
                database.add_seal(
                    seal_id=seal_info['id'],
                    name=seal_info['name'],
                    category=seal_info['category'],
                    image_path=seal_info['path'],
                    features=features
                )
                
                print(f"Added {seal_info['name']} to database")
                
            except Exception as e:
                print(f"Error adding {seal_info['name']}: {e}")

def main():
    """Example evaluation workflow"""
    # Initialize system
    database = SealDatabase()
    detector = ForgeryDetector(database)
    evaluator = SealEvaluator(detector)
    
    # Create sample database (optional)
    create_sample_database()
    
    # Run evaluation if test data exists
    test_data_path = "test_data"
    if os.path.exists(test_data_path):
        print("Running batch evaluation...")
        results = evaluator.batch_evaluate(test_data_path)
        
        # Generate report
        report = evaluator.generate_report(results)
        
        print("\nEvaluation Results:")
        print(f"Total Authentic Tested: {report['summary']['total_authentic_tested']}")
        print(f"Total Forged Tested: {report['summary']['total_forged_tested']}")
        print(f"Correctly Identified Authentic: {report['summary']['correctly_identified_authentic']}")
        print(f"Correctly Identified Forged: {report['summary']['correctly_identified_forged']}")
        
        if report['metrics']:
            print(f"\nPerformance Metrics:")
            for metric, value in report['metrics'].items():
                print(f"{metric.capitalize()}: {value:.3f}")
        
        # Generate plots
        evaluator.plot_results(results)
        
    else:
        print(f"Test data directory not found: {test_data_path}")
        print("Please create the directory structure:")
        print("test_data/")
        print("├── authentic/")
        print("└── forged/")

if __name__ == "__main__":
    main()