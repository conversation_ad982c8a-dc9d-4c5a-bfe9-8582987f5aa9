# Seal Forgery Detection System

An advanced computer vision system for detecting forged seals/stamps through comprehensive similarity analysis using OpenCV and machine learning techniques.

## Features

- **Advanced Preprocessing**: Red color extraction, morphological operations, polar coordinate transformation
- **Multi-Feature Analysis**: SIFT, ORB, texture (LBP), edge features
- **Geometric Analysis**: Hough circle detection for circular seals
- **Comprehensive Similarity Scoring**: Weighted combination of multiple feature types
- **Database Management**: SQLite-based storage for authentic seal templates
- **GUI Application**: User-friendly interface for easy interaction
- **Batch Evaluation**: Performance testing and metrics calculation
- **Forgery Detection**: Threshold-based authenticity assessment

## Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd seal-forgery-detector
```

2. Install dependencies:
```bash
pip install -r requirements.txt
```

## Usage

### GUI Application

Run the graphical interface:
```bash
python seal_detector_gui.py
```

Features:
- Select and analyze seal images
- View detection results with similarity scores
- Add authentic seals to database
- View database contents

### Command Line Usage

```python
from seal_forgery_detector import SealDatabase, ForgeryDetector

# Initialize system
database = SealDatabase()
detector = ForgeryDetector(database)

# Detect forgery
results = detector.detect_forgery("test_seal.jpg")

# Print results
for i, match in enumerate(results, 1):
    print(f"Rank {i}: {match.template_id} - {match.similarity_score:.3f}")
    print(f"Status: {'AUTHENTIC' if match.is_authentic else 'FORGERY'}")
```

### Batch Evaluation

```bash
python seal_evaluation.py
```

Requires test data structure:
```
test_data/
├── authentic/
│   ├── seal1.jpg
│   └── seal2.jpg
└── forged/
    ├── fake1.jpg
    └── fake2.jpg
```

## System Architecture

### Core Components

1. **SealPreprocessor**: Image preprocessing and enhancement
   - Red color extraction using HSV color space
   - Morphological operations for noise reduction
   - Hough circle detection
   - Polar coordinate transformation

2. **AdvancedFeatureExtractor**: Multi-modal feature extraction
   - SIFT keypoints and descriptors
   - ORB features for robustness
   - Local Binary Pattern (LBP) texture features
   - Edge density and orientation features

3. **SealDatabase**: Efficient storage and retrieval
   - SQLite database for metadata
   - Pickle serialization for feature vectors
   - CRUD operations for seal templates

4. **ForgeryDetector**: Main detection engine
   - FLANN-based feature matching
   - Cosine similarity for texture comparison
   - Weighted similarity scoring
   - Threshold-based authenticity assessment

### Detection Algorithm

1. **Preprocessing**:
   - Extract red regions (typical seal color)
   - Apply morphological operations
   - Detect circular boundaries
   - Convert to polar coordinates (rotation invariant)

2. **Feature Extraction**:
   - SIFT keypoints for distinctive features
   - Texture analysis using LBP
   - Edge pattern analysis
   - Geometric feature extraction

3. **Similarity Analysis**:
   - Match SIFT descriptors using FLANN
   - Calculate texture similarity
   - Analyze edge patterns
   - Combine scores with weights

4. **Forgery Detection**:
   - Compare against authentic database
   - Apply authenticity threshold
   - Rank by similarity scores
   - Generate confidence metrics

## Performance Metrics

The system evaluates performance using:
- **Accuracy**: Overall correct classifications
- **Precision**: True positives / (True positives + False positives)
- **Recall**: True positives / (True positives + False negatives)
- **F1-Score**: Harmonic mean of precision and recall

## Configuration

Key parameters in `ForgeryDetector`:
- `authenticity_threshold`: 0.7 (minimum similarity for authentic classification)
- `sift_match_threshold`: 0.75 (Lowe's ratio test threshold)

Feature weights in similarity calculation:
- SIFT features: 40%
- Texture features: 30%
- Edge features: 20%
- Polar texture: 10%

## Advanced Features

### Rotation Invariance
- Polar coordinate transformation handles rotated seals
- SIFT features are inherently rotation-invariant

### Scale Invariance
- SIFT descriptors handle scale variations
- Normalized feature vectors

### Noise Robustness
- Morphological operations reduce noise
- Multiple feature types provide redundancy

### Color Analysis
- HSV color space for robust red extraction
- Handles lighting variations

## Future Enhancements

1. **Deep Learning Integration**:
   - CNN-based feature extraction
   - Siamese networks for similarity learning
   - Transfer learning from pre-trained models

2. **OCR Integration**:
   - Text extraction and verification
   - Font analysis for authenticity

3. **Advanced Geometric Analysis**:
   - Shape analysis beyond circles
   - Symmetry detection

4. **Real-time Processing**:
   - Optimized algorithms
   - GPU acceleration

## Troubleshooting

### Common Issues

1. **"No matches found"**: Ensure authentic seals are in database
2. **Low accuracy**: Adjust `authenticity_threshold` parameter
3. **Slow processing**: Reduce image resolution or feature count

### Performance Tips

- Use high-quality, well-lit seal images
- Ensure seals are properly cropped
- Maintain consistent image formats
- Regular database maintenance

## License

This project is for educational and research purposes. Please ensure compliance with local laws regarding seal verification and forgery detection.

## Contributing

1. Fork the repository
2. Create feature branch
3. Commit changes
4. Push to branch
5. Create Pull Request

## Support

For issues and questions, please create an issue in the repository or contact the development team.